import "@/styles/globals.css";
import { AppCacheProvider } from '@mui/material-nextjs/v15-pagesRouter';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Roboto } from 'next/font/google';
import { GoogleAnalytics } from '@next/third-parties/google'

const roboto = Roboto({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto',
});

const theme = createTheme({
  typography: {
    fontFamily: roboto.variable,
  },
});

export const metadata = {
  title: 'Subanet',
  description: 'Subanet Limited is a UK based company that provides IT services and solutions to businesses and individuals.',
  author: 'Subanet Limited - Taner Subasi',
}
  
export default function App(props) {
  const { Component, pageProps } = props;
  return <AppCacheProvider {...props}>
    <ThemeProvider theme={theme}>
      <main className={roboto.variable}>
        <Component {...pageProps} />
      </main>
    </ThemeProvider>

          <GoogleAnalytics gaId="G-7LP9CLGWJG" />
  </AppCacheProvider>;
}
