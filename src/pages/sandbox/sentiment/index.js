import {useEffect, useRef, useState} from 'react';
import Image from "next/image";
import Head from 'next/head';
import localFont from "next/font/local";

import Wave from 'react-wavify'
import { GoogleAnalytics, sendGAEvent } from '@next/third-parties/google'
import { styled } from '@mui/material/styles';

import { AlertDialogSlide } from '/components/contactDialog.js';

const geistSans = localFont({
    src: "../../fonts/GeistVF.woff",
    variable: "--font-geist-sans",
    weight: "100 900",
});
const geistMono = localFont({
    src: "../../fonts/GeistMonoVF.woff",
    variable: "--font-geist-mono",
    weight: "100 900",
});

import ReportGmailerrorredIcon from '@mui/icons-material/ReportGmailerrorred';
import Card from '@mui/material/Card';
import { Stack, Typography, CardContent, Grid2 as Grid, <PERSON>, TextField, Button, LinearProgress } from "@mui/material";

export default function Home() {

    const mdlRef = useRef(null);
    const [focusedCardIndex, setFocusedCardIndex] = useState(
        null,
    );

    const [text, setText] = useState('');
    const [sentiment, setSentiment] = useState(null);
    const [analysing, setanalysing] = useState(false);
    

    useEffect(() => { 
        try { sendGAEvent('event', 'PageLoaded_sentimentAnalysis', { dt: new Date(Date.now()).toISOString() }); }
        catch (e) { console.error(e); }
     }, []) // 

    const showModal = () => {
        try { sendGAEvent('event', 'showContactUsModal', { value: new Date(Date.now()).toISOString() }); }
        catch (e) { console.error(e); }
        mdlRef.current && mdlRef.current.showModal();
    }
    const handleFocus = (index) => {
        try { sendGAEvent('event', 'sectionFocusClicked', { index, value: cardData[index]?.title }); }
        catch (e) { console.error(e); }
        setFocusedCardIndex(index);
    };

    const handleBlur = () => {
        setFocusedCardIndex(null);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setanalysing(true);
        try { sendGAEvent('event', 'startAnalyze_sentimentAnalysis', { value: JSON.stringify({ texts: text }), dt: new Date(Date.now()).toISOString() }); }
        catch (e) { console.error(e); }

        try {
            const response = await fetch('/api/sentiment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': 'SubanetSandboxSentimentAnaylsis',
                },
                body: JSON.stringify({ texts: text }),
            });
            const data = await response.json();
            // console.log('json response', data)
            setSentiment(data);
            setanalysing(false);
        } catch (error) {
            console.error('Error:', error);
            setanalysing(false);
        }
    };

    return (
        <>
            <Head>
                <title>Sentiment Analysis - Sandbox - Subanet</title>
                <meta name="description" content="Subanet Limited sandbox. Sentiment analysis. you can analyze sentiment across multiple languages, enhancing your global reach. Supports English plus Chinese (中文), Spanish (Español), Hindi (हिन्दी), Arabic (العربية), Bengali (বাংলা), Portuguese (Português), Russian (Русский), Japanese (日本語), German (Deutsch), Malay (Bahasa Melayu), Telugu (తెలుగు), Vietnamese (Tiếng Việt), Korean (한국어), French (Français), Turkish (Türkçe), Italian (Italiano), Polish (Polski), Ukrainian (Українська), Tagalog, Dutch (Nederlands), Swiss German (Schweizerdeutsch). Text Classification (Sentiment Analysis). Usage: Social media analysis, Customer feedback analysis, Product reviews classification, Brand monitoring, Market research, Customer service optimization, Competitive intelligence " />
                <meta property="og:title" content="Subanet - Welcome" />
                <meta property="og:description" content="Subanet Limited sandbox. Sentiment analysis. you can analyze sentiment across multiple languages, enhancing your global reach. Supports English plus Chinese (中文), Spanish (Español), Hindi (हिन्दी), Arabic (العربية), Bengali (বাংলা), Portuguese (Português), Russian (Русский), Japanese (日本語), German (Deutsch), Malay (Bahasa Melayu), Telugu (తెలుగు), Vietnamese (Tiếng Việt), Korean (한국어), French (Français), Turkish (Türkçe), Italian (Italiano), Polish (Polski), Ukrainian (Українська), Tagalog, Dutch (Nederlands), Swiss German (Schweizerdeutsch). Text Classification (Sentiment Analysis). Usage: Social media analysis, Customer feedback analysis, Product reviews classification, Brand monitoring, Market research, Customer service optimization, Competitive intelligence " />
                <meta property="og:image" content="https://subanet.com/logo.svg" />
                <meta name="twitter:card" content="summary_large_image" />
                <meta name='keywords' content='subanet, sandbox, sentiment analysis, text classification, sentiment analysis, social media analysis, customer feedback analysis, product reviews classification, brand monitoring, market research, customer service optimization, competitive intelligence' />
            </Head>
            <main className={`${geistSans.variable} ${geistMono.variable} font-[family-name:var(--font-geist-sans)]`}>
                {/* Navigation */}
                <div
                    className="fixed inset-0 -z-10"
                    style={{
                        // backgroundImage: 'url("/bg.jpg")',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat',
                        backgroundColor: 'rgba(255,255,255,0.95)',
                    }}
                />
                <nav className="fixed w-full bg-white/70 backdrop-blur-sm shadow-sm z-50">
                    <div className="container mx-auto px-6 py-4 flex justify-between items-center">
                        <Stack direction="row" spacing={2} alignItems="center">
                            <Image
                                src="/logos/sandboxLogo_t.png"
                                width={80}
                                height={20}
                                alt="Subanet Logo"
                            />
                            <div className="text-2xl font-bold text-indigo-600" onClick={() => document.location = '/'}><Image
                                src="/logo.svg"
                                width={40}
                                height={9}
                                alt="Subanet Logo"
                            />
                            </div>
                        </Stack>


                        <div className=" md:flex space-x-8">
                            {/* <a href="#home" className="hover:text-indigo-600 text-sky-500">Home</a> */}
                            <a href="#contact" onClick={showModal} className="hover:text-indigo-600 text-sky-500 ">
                                <Typography variant={'body1'} sx={{ fontSize: { xs: '12px', md: '18px' } }}>Contact Us</Typography>
                            </a>
                        </div>
                    </div>
                </nav>

                <section id="home" className="min-h-screen flex items-center pt-20">
                    <div className="container mx-auto px-6 flex flex-col md:flex-row items-center">
                        <div className="md:w-full space-y-8 mb-12">
                            <div className="backdrop-blur-sm bg-white/30 p-2 rounded-lg my-4">
                                <Card sx={{ p: 1, py: 1, mb: 0, borderWidth: 0, borderColor: '#ccc' }} className="bg-white/30 backdrop-blur-sm shadow-sm z-50">

                                    {/* <Typography gutterBottom variant="caption" component="div">
              Prepare Your Digital Infrastructure for the Future
                          </Typography> */}
                                    <Typography variant="h2" component="h2" className="text-gray-900 mb-2" sx={{ px: 2, fontSize: { xs: '32px', md: '54px' } }}>
                                        Sentiment Analysis
                                    </Typography>
                                </Card>


                                <Grid container spacing={2} columns={12}>

                                    <Grid size={{ xs: 12, md: 12 }}>
                                        <Box sx={{ maxHeight: 30, overflow: 'hidden', minHeight: 30 }}>
                                            <Wave fill='#efefef' fillx='#bababa'
                                                paused={false}
                                                style={{ display: 'flex' }}
                                                options={{
                                                    height: 10,
                                                    amplitude: 20,
                                                    speed: 0.15,
                                                    points: 3
                                                }}
                                            />
                                        </Box>

                                    </Grid>
                                    <Grid size={{ xs: 12, md: 12 }}>
                                        <SyledCard
                                            // variant="outlined"
                                            // sx={{ m: 2 }}
                                            sx={{ p: 1, py: 2, mb: 2, flex: 1, borderWidth: 0.5, borderColor: '#ccc' }}
                                            // className="bg-white/10 backdrop-blur-sm shadow-sm z-50"
                                            onFocus={() => handleFocus('f')}
                                            onMouseOver={() => handleFocus('f')}
                                            onBlur={handleBlur}
                                            tabIndex={0}
                                            className={focusedCardIndex === 'f' ? 'Mui-focused bg-white/90 backdrop-blur-sm shadow-sm z-50' : 'bg-white/50 backdrop-blur-sm shadow-sm z-50'}
                                        >
                                            <SyledCardContent>
                                                <Typography gutterBottom variant="caption" component="div">
                                                    enter text to analyze
                                                </Typography>
                                                {/* form alanı */}

                                                <form onSubmit={handleSubmit}>
                                                    <TextField
                                                        fullWidth
                                                        multiline
                                                        rows={4}
                                                        value={text}
                                                        onChange={(e) => setText(e.target.value)}
                                                        placeholder="Enter text to analyze"
                                                        variant="outlined"
                                                        sx={{ mb: 2 }}
                                                    />
                                                    <Button
                                                        variant="contained"
                                                        type="submit"
                                                        fullWidth
                                                        loading={analysing.toString()}
                                                        disabled={analysing}
                                                    >
                                                        {analysing ? 'Analyzing...' : 'Analyze'}
                                                    </Button>
                                                    {/* {sentiment && (
                                                        <Typography variant="body1" sx={{ mt: 2 }}>
                                                            Sentiment: {JSON.stringify(sentiment)}
                                                        </Typography>
                                                    )} */}
                                                </form>

                                            </SyledCardContent>
                                        </SyledCard>
                                    </Grid>


                                    <Grid size={{ xs: 12, md: 6 }}>
                                        <SyledCard
                                            // variant="outlined"
                                            // sx={{ m: 2 }}
                                            sx={{ p: 1, py: 2, mb: 2, flex: 1, borderWidth: 0.5, borderColor: '#ccc' }}
                                            // className="bg-white/10 backdrop-blur-sm shadow-sm z-50"
                                            onFocus={() => handleFocus('r')}
                                            onMouseOver={() => handleFocus('r')}
                                            onBlur={handleBlur}
                                            tabIndex={'r'}
                                            className={focusedCardIndex === 'r' ? 'Mui-focused bg-white/90 backdrop-blur-sm shadow-sm z-50' : 'bg-white/50 backdrop-blur-sm shadow-sm z-50'}
                                        >
                                            <SyledCardContent>
                                                <Typography gutterBottom variant="caption" component="div">
                                                    {'analysis results'}
                                                </Typography>
                                                {sentiment && (
                                                    <>
                                                        <Typography variant="h4" component="h2" sx={{ fontSize: { xs: '18px', md: '32px' } }} className="text-gray-900 mb-2">
                                                        {sentiment?.sentiments[0].sentiment_value} {(sentiment?.reviewNeeded && <span title='Review needed'><ReportGmailerrorredIcon sx={{color:'red', fontSize: '64px'}} /></span>)}
                                                        </Typography>
                                                        <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 2 }}>
                                                            <Box sx={{ width: '200px' }}>
                                                                <Typography variant="body1" className="text-gray-800" sx={{ fontSize: '18px' }}>
                                                                    Very Positive:
                                                                </Typography>
                                                            </Box>
                                                            <Box sx={{ width: '100%' }}>
                                                                <LinearProgress variant="determinate" color='primary' value={sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Very Positive')[0].score * 100} />
                                                                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '12px' }}>
                                                                    {sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Very Positive')[0].score}
                                                                </Typography>
                                                            </Box>
                                                        </Stack>
                                                        <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 2 }}>
                                                            <Box sx={{ width: '200px' }}>
                                                                <Typography variant="body1" className="text-gray-800" sx={{ fontSize: '18px' }}>
                                                                    Positive:
                                                                </Typography>
                                                            </Box>
                                                            <Box sx={{ width: '100%' }}>
                                                                <LinearProgress variant="determinate" color='primary' value={sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Positive')[0].score * 100} />
                                                                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '12px' }}>
                                                                    {sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Positive')[0].score}
                                                                </Typography>
                                                            </Box>
                                                        </Stack>
                                                        <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 2 }}>
                                                            <Box sx={{ width: '200px' }}>
                                                                <Typography variant="body1" className="text-gray-800" sx={{ fontSize: '18px' }}>
                                                                    Neutral:
                                                                </Typography>
                                                            </Box>
                                                            <Box sx={{ width: '100%' }}>
                                                                <LinearProgress variant="determinate" color='inherit' sx={{color: '#aaa', backgroundColor: '#ddd'}} value={sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Neutral')[0].score * 100} />
                                                                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '12px' }}>
                                                                    {sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Neutral')[0].score}
                                                                </Typography>
                                                            </Box>
                                                        </Stack>

                                         
                                                        <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 2 }}>
                                                            <Box sx={{ width: '200px' }}>
                                                                <Typography variant="body1" className="text-gray-800" sx={{ fontSize: '18px' }}>
                                                                    Negative:
                                                                </Typography>
                                                            </Box>
                                                            <Box sx={{ width: '100%' }}>
                                                                <LinearProgress variant="determinate" color='warning' value={sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Negative')[0].score * 100} />
                                                                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '12px' }}>
                                                                    {sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Negative')[0].score}
                                                                </Typography>
                                                            </Box>
                                                        </Stack>

                                                        <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 2 }}>
                                                            <Box sx={{ width: '200px' }}>
                                                                <Typography variant="body1" className="text-gray-800" sx={{ fontSize: '18px' }}>
                                                                    Very Negative:
                                                                </Typography>
                                                            </Box>
                                                            <Box sx={{ width: '100%' }}>
                                                                <LinearProgress variant="determinate" color='warning' value={sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Very Negative')[0].score * 100} />
                                                                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '12px' }}>
                                                                    {sentiment?.sentiments[0].probabilities.filter(p => p.sentiment === 'Very Negative')[0].score}
                                                                </Typography>
                                                            </Box>
                                                        </Stack>
{/*                                                         
                                                        <StyledTypography variant="body2" color="text.secondary" gutterBottom>
                                                            {'cardData[index].descMore'}
                                                        </StyledTypography> */}

                                                    </>
                                                )}
                                            </SyledCardContent>
                                        </SyledCard>
                                    </Grid>


                                    <Grid size={{ xs: 12, md: 6 }}>
                                        <SyledCard
                                            // variant="outlined"
                                            // sx={{ m: 2 }}
                                            sx={{ p: 1, py: 2, mb: 2, flex: 1, borderWidth: 0.5, borderColor: '#ccc' }}
                                            // className="bg-white/10 backdrop-blur-sm shadow-sm z-50"
                                            onFocus={() => handleFocus('r')}
                                            onMouseOver={() => handleFocus('r')}
                                            onBlur={handleBlur}
                                            tabIndex={'r'}
                                            className={focusedCardIndex === 'r' ? 'Mui-focused bg-white/90 backdrop-blur-sm shadow-sm z-50' : 'bg-white/50 backdrop-blur-sm shadow-sm z-50'}
                                        >
                                            <SyledCardContent>
                                                <Typography variant="h4" component="h2" sx={{ fontSize: { xs: '18px', md: '32px' } }} className="text-gray-900 mb-2">
                                                            {'Model'}
                                                        </Typography>
                                                <Typography gutterBottom variant="caption" component="a" href="https://huggingface.co/distilbert/distilbert-base-multilingual-cased" target="_blank" rel="noreferrer">
                                                    tabularisai/multilingual-sentiment-analysis | distilbert/distilbert-base-multilingual-cased
                                                </Typography> 

                                                <Typography variant="body2" className="text-gray-800">
                                                Supports English plus Chinese (中文), Spanish (Español), Hindi (हिन्दी), Arabic (العربية), Bengali (বাংলা), Portuguese (Português), Russian (Русский), Japanese (日本語), German (Deutsch), Malay (Bahasa Melayu), Telugu (తెలుగు), Vietnamese (Tiếng Việt), Korean (한국어), French (Français), Turkish (Türkçe), Italian (Italiano), Polish (Polski), Ukrainian (Українська), Tagalog, Dutch (Nederlands), Swiss German (Schweizerdeutsch).
                                                
                                                </Typography>

                                                <Typography gutterBottom variant="caption" component="div" sx={{ mt: 2 }}>
                                                    {'Ideal for'}
                                                </Typography> 

                                                <Stack sx={{ flexDirection: 'row', justifyContent: 'flex-start' }} variant="body2" className="text-gray-800">
                                                    <Box sx={{ flex: 1 }}>
                                                        <Typography>Social media analysis</Typography>
                                                        <Typography>Customer feedback analysis</Typography>
                                                        <Typography>Product reviews classification</Typography>
                                                    </Box>
                                                    <Box sx={{ flex: 1 }}>
                                                        <Typography>Brand monitoring</Typography>
                                                        <Typography>Market research</Typography>
                                                        <Typography>Customer service optimization</Typography>
                                                        <Typography>Competitive intelligence</Typography>
                                                    </Box>
                                                </Stack>

                                            </SyledCardContent>
                                        </SyledCard>
                                    </Grid>

                                </Grid>

                                <Box sx={{ textAlign: 'center' }}>
                                    <AlertDialogSlide text={'Contact Us'} ref={mdlRef} />
                                </Box>
                            </div>
                        </div>
                        {/* <div className="md:w-1/2 mt-12 md:mt-0">
            <Image
              src="/hero-image.svg"
              alt="Hero"
              width={600}
              height={400}
              priority
              className="rounded-[5px] shadow-lg"
            />
          </div> */}
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12">
                    <div className="container mx-auto px-6 text-center">
                        <p>1989-2025 Subanet.</p>
                    </div>
                </footer>
            </main>
        </>
    );
}

const cardData = [
    {
        img: 'https://picsum.photos/800/450?random=1',
        tag: 'Prepare Your Digital Infrastructure for the Future',
        title: 'Digital Transformation Solutions',
        description:
            'Transform your digital infrastructure, redesign business processes, and enhance efficiency.',
        descMore: 'We help transform your business processes and product offerings to meet the demands of the digital age. From migrating to modern ERP systems like SAP Hana to completely reengineering your workflows, we make your organization more agile and competitive.',
        authors: [
            { name: 'Remy Sharp', avatar: '/static/images/avatar/1.jpg' },
            { name: 'Travis Howard', avatar: '/static/images/avatar/2.jpg' },
        ],
    },
    {
        img: 'https://picsum.photos/800/450?random=2',
        tag: 'Automate Your Processes for Time and Cost Savings',
        title: 'Operational Efficiency & Automation',
        description:
            'Leverage Robotic Process Automation (RPA) to streamline your operations, saving time and reducing costs.',
        descMore: 'Optimize your operations and maximize workforce efficiency by automating repetitive tasks. Through Robotic Process Automation (RPA), we automate critical business processes, allowing your employees to focus on more strategic work.',
        authors: [{ name: 'Erica Johns', avatar: '/static/images/avatar/6.jpg' }],
    },
    {
        img: 'https://picsum.photos/800/450?random=3',
        tag: 'Strategically Manage and Develop Your Product Roadmap',
        title: 'Strategic Product Management & Development',
        description:
            'Align product roadmaps with corporate objectives to respond quickly to evolving market needs.',
        descMore: 'Align your product development process with corporate goals and market demands for a competitive advantage. We offer CRM systems and sales intelligence reporting to help you make data-driven decisions and boost product performance.',
        authors: [{ name: 'Kate Morrison', avatar: '/static/images/avatar/7.jpg' }],
    },
    {
        img: 'https://picsum.photos/800/450?random=4',
        tag: 'Reduce Costs with Sustainable Solutions',
        title: "Sustainability & Cost Reduction",
        description:
            "Reduce your carbon footprint with digital invoicing and sustainable operational solutions.",
        descMore: 'Develop sustainable business processes that reduce both your environmental impact and operational costs. By transitioning workflows, we help your business significantly cut expenses.',
        authors: [{ name: 'Cindy Baker', avatar: '/static/images/avatar/3.jpg' }],
    },
];

const SyledCard = styled(Card)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    padding: 0,
    height: '100%',
    backgroundColor: (theme.vars || theme).palette.background.paper,
    '&:hover': {
        // backgroundColor: 'transparent',
        outline: '3px solid',
        // outlineColor: 'hsla(210, 98%, 48%, 0.5)',
        outlineColor: 'hsla(210, 98%, 48%, 0.2)',
        outlineOffset: '2px',
        cursor: 'pointer',
    },
    '&:focus-visible': {
        outline: '3px solid',
        outlineColor: 'hsla(210, 98%, 48%, 0.5)',
        outlineOffset: '2px',
    },
}));

const SyledCardContent = styled(CardContent)({
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
    padding: 16,
    flexGrow: 1,
    '&:last-child': {
        paddingBottom: 16,
    },
});

const StyledTypography = styled(Typography)({
    display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
    // WebkitLineClamp: 6,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});
