/* eslint-disable react/display-name */
import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import Image from "next/image";
import Head from 'next/head';
import localFont from "next/font/local";

import { useDropzone } from 'react-dropzone';
import Wave from 'react-wavify'
import { GoogleAnalytics, sendGAEvent } from '@next/third-parties/google'
import { styled } from '@mui/material/styles';

import SaveIcon from '@mui/icons-material/Save';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { AlertDialogSlide } from '/components/contactDialog.js';

const geistSans = localFont({
    src: "../../fonts/GeistVF.woff",
    variable: "--font-geist-sans",
    weight: "100 900",
});
const geistMono = localFont({
    src: "../../fonts/GeistMonoVF.woff",
    variable: "--font-geist-mono",
    weight: "100 900",
});
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import ReportGmailerrorredIcon from '@mui/icons-material/ReportGmailerrorred';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import Card from '@mui/material/Card';
import Collapse from '@mui/material/Collapse';
import Slide from '@mui/material/Slide';
import { Stack, Typography, CardContent, Grid2 as Grid, Box, TextField, Button, LinearProgress } from "@mui/material";

// const ODD_OPACITY = 0.2;
// const keyHead = 'dex:';
// const Transition = React.forwardRef(function Transition(props, ref) {
//   return <Slide direction="up" ref={ref} {...props} />;
// });

export default function Home() {

    const mdlRef = useRef(null);
    const fileRef = useRef(null);
    const [focusedCardIndex, setFocusedCardIndex] = useState(
        null,
    );

    const [text, setText] = useState('');
    const [filedata, setfiledataR] = useState(false);
    const [collapsed, setcollapsed] = useState(false);

    const setfiledata = (data) => {
        if (data) {
            setfiledataR(data);
            setcollapsed(true);
        } else {
            setfiledataR(false);
            fileRef.current.resetForm();
        }
    }

    useEffect(() => {
        try { sendGAEvent('event', 'PageLoaded_sentimentAnalysis', { dt: new Date(Date.now()).toISOString() }); }
        catch (e) { console.error(e); }
    }, []) // 

    const showModal = () => {
        try { sendGAEvent('event', 'showContactUsModal', { value: new Date(Date.now()).toISOString() }); }
        catch (e) { console.error(e); }
        mdlRef.current && mdlRef.current.showModal();
    }
    const handleFocus = (index) => {
        try { sendGAEvent('event', 'sectionFocusClicked', { index, value: cardData[index]?.title }); }
        catch (e) { console.error(e); }
        setFocusedCardIndex(index);
    };

    const handleBlur = () => {
        setFocusedCardIndex(null);
    };

    // const handleSubmit = async (e) => {
    //     e.preventDefault();
    //     setanalysing(true);
    //     try { sendGAEvent('event', 'startAnalyze_sentimentAnalysis', { value: JSON.stringify({ texts: text }), dt: new Date(Date.now()).toISOString() }); }
    //     catch (e) { console.error(e); }

    //     try {
    //         const response = await fetch('/api/pdfextract', {
    //             method: 'POST',
    //             headers: {
    //                 'Content-Type': 'application/json',
    //                 'x-api-key': 'SubanetSandboxPdfTextExtract',
    //             },
    //             body: JSON.stringify({ texts: text }),
    //         });
    //         const data = await response.json();
    //         // console.log('json response', data)
    //         setSentiment(data);
    //         setanalysing(false);
    //     } catch (error) {
    //         console.error('Error:', error);
    //         setanalysing(false);
    //     }
    // };

    return (
        <>
            <Head>
                <title>Sentiment Analysis - Sandbox - Subanet</title>
                <meta name="description" content="Subanet Limited sandbox. Sentiment analysis. you can analyze sentiment across multiple languages, enhancing your global reach. Supports English plus Chinese (中文), Spanish (Español), Hindi (हिन्दी), Arabic (العربية), Bengali (বাংলা), Portuguese (Português), Russian (Русский), Japanese (日本語), German (Deutsch), Malay (Bahasa Melayu), Telugu (తెలుగు), Vietnamese (Tiếng Việt), Korean (한국어), French (Français), Turkish (Türkçe), Italian (Italiano), Polish (Polski), Ukrainian (Українська), Tagalog, Dutch (Nederlands), Swiss German (Schweizerdeutsch). Text Classification (Sentiment Analysis). Usage: Social media analysis, Customer feedback analysis, Product reviews classification, Brand monitoring, Market research, Customer service optimization, Competitive intelligence " />
                <meta property="og:title" content="Subanet - Welcome" />
                <meta property="og:description" content="Subanet Limited sandbox. Sentiment analysis. you can analyze sentiment across multiple languages, enhancing your global reach. Supports English plus Chinese (中文), Spanish (Español), Hindi (हिन्दी), Arabic (العربية), Bengali (বাংলা), Portuguese (Português), Russian (Русский), Japanese (日本語), German (Deutsch), Malay (Bahasa Melayu), Telugu (తెలుగు), Vietnamese (Tiếng Việt), Korean (한국어), French (Français), Turkish (Türkçe), Italian (Italiano), Polish (Polski), Ukrainian (Українська), Tagalog, Dutch (Nederlands), Swiss German (Schweizerdeutsch). Text Classification (Sentiment Analysis). Usage: Social media analysis, Customer feedback analysis, Product reviews classification, Brand monitoring, Market research, Customer service optimization, Competitive intelligence " />
                <meta property="og:image" content="https://subanet.com/logo.svg" />
                <meta name="twitter:card" content="summary_large_image" />
                <meta name='keywords' content='subanet, sandbox, sentiment analysis, text classification, sentiment analysis, social media analysis, customer feedback analysis, product reviews classification, brand monitoring, market research, customer service optimization, competitive intelligence' />
            </Head>
            <main className={`${geistSans.variable} ${geistMono.variable} font-[family-name:var(--font-geist-sans)]`}>
                {/* Navigation */}
                <div
                    className="fixed inset-0 -z-10"
                    style={{
                        // backgroundImage: 'url("/bg.jpg")',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat',
                        backgroundColor: 'rgba(255,255,255,0.95)',
                    }}
                />
                <nav className="fixed w-full bg-white/70 backdrop-blur-sm shadow-sm z-50">
                    <div className="container mx-auto px-6 py-4 flex justify-between items-center">
                        <Stack direction="row" spacing={2} alignItems="center">
                            <Image
                                src="/logos/sandboxLogo_t.png"
                                width={80}
                                height={20}
                                alt="Subanet Logo"
                            />
                            <div className="text-2xl font-bold text-indigo-600" onClick={() => document.location = '/'}><Image
                                src="/logo.svg"
                                width={40}
                                height={9}
                                alt="Subanet Logo"
                            />
                            </div>
                        </Stack>


                        <div className=" md:flex space-x-8">
                            {/* <a href="#home" className="hover:text-indigo-600 text-sky-500">Home</a> */}
                            <a href="#contact" onClick={showModal} className="hover:text-indigo-600 text-sky-500 ">
                                <Typography variant={'body1'} sx={{ fontSize: { xs: '12px', md: '18px' } }}>Contact Us</Typography>
                            </a>
                        </div>
                    </div>
                </nav>

                <section id="home" className="min-h-screen flex items-start pt-20">
                    <div className="container mx-auto px-6 flex flex-col md:flex-row items-center">
                        <div className="md:w-full space-y-8 mb-12">
                            <div className="backdrop-blur-sm bg-white/30 p-2 rounded-lg my-4">
                                <Card sx={{ p: 1, py: 1, mb: 0, borderWidth: 0, borderColor: '#ccc' }} className="bg-white/30 backdrop-blur-sm shadow-sm z-50">

                                    <Typography gutterBottom variant="caption" component="div">
                                        convert your PDF documents to text format
                                    </Typography>
                                    <Typography variant="h2" component="h2" className="text-gray-900 mb-2" sx={{ px: 2, fontSize: { xs: '32px', md: '54px' } }}>
                                        PDF Extractor - Text
                                    </Typography>
                                </Card>

                                <Grid container spacing={2} columns={12}>


                                    <Grid size={{ xs: 12, md: 12 }}>
                                        <FileUploadForm ref={fileRef} filedata={filedata} setfiledata={setfiledata} />
                                    </Grid>
                                    <Grid size={{ xs: 12, md: 12 }}>
                                        {filedata && <PDFData setfiledata={setfiledata} data={filedata} />}
                                    </Grid>

                                </Grid>

                                <Box sx={{ textAlign: 'center' }}>
                                    <AlertDialogSlide text={'Contact Us'} ref={mdlRef} />
                                </Box>
                            </div>
                        </div>

                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12">
                    <div className="container mx-auto px-6 text-center">
                        <p>1989-2025 Subanet.</p>
                    </div>
                </footer>
            </main>
        </>
    );
}


const SyledCard = styled(Card)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    padding: 0,
    height: '100%',
    backgroundColor: (theme.vars || theme).palette.background.paper,
    '&:hover': {
        // backgroundColor: 'transparent',
        // outline: '3px solid',
        // outlineColor: 'hsla(210, 98%, 48%, 0.5)',
        // outlineColor: 'hsla(210, 98%, 48%, 0.2)',
        // outlineOffset: '2px',
        // cursor: 'pointer',
    },
    '&:focus-visible': {
        // outline: '3px solid',
        // outlineColor: 'hsla(210, 98%, 48%, 0.5)',
        // outlineOffset: '2px',
    },
}));

const SyledCardContent = styled(CardContent)({
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
    padding: 16,
    flexGrow: 1,
    '&:last-child': {
        paddingBottom: 16,
    },
});

const StyledTypography = styled(Typography)({
    display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
    // WebkitLineClamp: 6,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});



const FileUploadForm = forwardRef((props, ref) => {
    const [file, setFile] = useState(null);
    const [isUploading, setIsUploading] = useState(false);

    const [collapsed, setcollapsed] = useState(true);
    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        accept: {
            'application/pdf': ['.pdf']
        },
        onDrop: (acceptedFiles) => {
            if (acceptedFiles.length > 0) {
                setFile(acceptedFiles[0]);
            }
        },
        maxFiles: 1,
    });

    const handleSubmit = async (formData) => {
        if (!file) return;
        setIsUploading(true);
        try {
            const response = await fetch('/api/pdfextract', {
                method: 'POST',
                headers: {
                    'x-api-key': 'SubanetSandboxPdfTextExtract',
                },
                body: formData,
            });
            const data = await response.json();
            props.setfiledata && props.setfiledata(data);
            setcollapsed(false);
            setIsUploading(false);
        } catch (error) {
            console.error('Upload error:', error);
            setIsUploading(false);
        } finally {
            setIsUploading(false);
            try { sendGAEvent('event', 'fileUploaded', { dt: new Date(Date.now()).toISOString() }); }
            catch (e) { console.error(e); }
        }
    };

    useImperativeHandle(ref, () => ({
        async resetForm() {
            setcollapsed(true);
            setFile(null);
            // try { sendGAEvent('event', 'fileUploadReset', { dt: new Date(Date.now()).toISOString() }); }
            // catch (e) { console.error(e); } 
        },
    }));

    return (
        <SyledCard>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, borderWidth: 1, backgroundColor: '#f9f9f9', borderColor: '#ccc' }}>
                <Typography onClick={() => setcollapsed(true)} sx={{ cursor: 'pointer' }} variant={'body1'}>Upload File</Typography>
                <Typography onClick={() => setcollapsed(!collapsed)}>
                    {collapsed ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                </Typography>
            </Box>

            <Collapse in={collapsed}>
                <SyledCardContent>
                    <div>
                        {/* <Typography variant="h4" class="text-gray-800 dark:text-gray-900 mt-2 " sx={{ mb: 2 }}>Select File</Typography> */}

                        <label for="format" class="text-gray-800 dark:text-gray-900 mt-2 " style={{ fontSize: '12px' }}>Format:</label>
                        <select id="format" name="format" class="text-gray-800 dark:text-gray-900 mt-2">
                            <option value="plain">Plain</option>
                            <option value="markdown">Markdown</option>
                        </select>

                        <div {...getRootProps()} style={{
                            border: '2px dashed #cccccc',
                            borderRadius: '4px',
                            padding: '20px',
                            textAlign: 'center',
                            cursor: 'pointer'
                        }}>
                            <input {...getInputProps()} />
                            {isDragActive ? (
                                <Typography>Drop the PDF file here...</Typography>
                            ) : (
                                <Typography sx={{ color: 'blue' }}>Drag and drop a PDF file here, or click to select file</Typography>
                            )}
                            {file && (
                                <Typography sx={{ color: 'blue', mt: 2 }} variant="body1" >{`Selected file: ${file.name} - ${file.size} bytes`}</Typography>
                            )}
                            {isUploading && (
                                <LinearProgress variant="indeterminate" color="primary" sx={{ position: 'absolute', left: 0, right: 0, top: 2, }} />
                            )}
                            {isUploading && (
                                <div role="status">
                                    {/* <svg aria-hidden="true" className="mr-2 w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor" />
                                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill" />
                                    </svg> */}
                                    <span className="sr-only">Uploading...</span>
                                </div>
                            )}
                        </div>
                        {file && (
                            <Button
                                variant="contained"
                                color="primary"
                                onClick={() => {
                                    const format = document.getElementById('format').value;
                                    const formData = new FormData();
                                    formData.append('file', file);
                                    formData.append('format', format);
                                    handleSubmit(formData);
                                }}
                                disabled={isUploading || file?.size > 5000000}
                                sx={{ mt: 2 }}
                            >
                                Upload PDF
                            </Button>
                        )}
                    </div>
                </SyledCardContent>
            </Collapse>
        </SyledCard>
    );
});
const PDFData = props => {
    return (
        <>
            <Box sx={{ maxHeight: 30, overflow: 'hidden', minHeight: 30 }}>
                <Wave fill='#efefef' fillx='#bababa'
                    paused={false}
                    style={{ display: 'flex' }}
                    options={{
                        height: 10,
                        amplitude: 20,
                        speed: 0.15,
                        points: 3
                    }}
                />
            </Box>

            <SyledCard>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, borderWidth: 1, backgroundColor: '#f9f9f9', borderColor: '#ccc' }}>
                    <Typography variant={'body2'}>Extracted Text</Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-even', alignItems: 'center', p: 2 }}>
                        <Typography title='Copy to clipboard' onClick={() => {
                            navigator.clipboard.writeText(props.data?.pdfText);
                            alert('Text copied to clipboard');

                            try { sendGAEvent('event', 'fileExtractCopied', { dt: new Date(Date.now()).toISOString() }); }
                            catch (e) { console.error(e); }

                        }}>
                            {<ContentCopyIcon />}
                        </Typography>
                        <Typography sx={{ mx: 2 }} title='Save' onClick={() => {
                            downloadText(props.data?.pdfText, props.data?.fileName.split('.')[0] + '.txt' || 'pdfText.txt');

                            try { sendGAEvent('event', 'fileExtractSaved', { dt: new Date(Date.now()).toISOString() }); }
                            catch (e) { console.error(e); }

                            }
                            } >
                            {<SaveIcon />}
                        </Typography>

                        <Typography title='Save' onClick={() => {
                            props.setfiledata(false);
                            try { sendGAEvent('event', 'fileExtractDeleted', { dt: new Date(Date.now()).toISOString() }); }
                            catch (e) { console.error(e); }
                        }
                            } >
                            {<DeleteForeverIcon />}
                        </Typography>

                    </Box>
                </Box>


                <SyledCardContent>
                    <p class="text-gray-800 dark:text-gray-900 mt-2 text-sm ">
                        <Typography
                            component={'div'}
                            style={{ whiteSpace: 'pre-line' }}>
                            {props.data?.pdfText}
                        </Typography>
                    </p>
                </SyledCardContent>
            </SyledCard>
        </>
    )
}


function downloadText(text, fileName = 'extracted_text.txt') {
    const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
