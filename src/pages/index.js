import * as React from 'react';
import Image from "next/image";
import Head from 'next/head';
import localFont from "next/font/local";

import Wave from 'react-wavify'
import { GoogleAnalytics, sendGAEvent } from '@next/third-parties/google'
import { styled } from '@mui/material/styles';

import { AlertDialogSlide } from '/components/contactDialog.js';

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

import Card from '@mui/material/Card';
import { Stack, Typography, CardContent, Grid2 as Grid, Box } from "@mui/material";

import CardMedia from '@mui/material/CardMedia';
import Link from 'next/link';


export default function Home() {

  const mdlRef = React.useRef(null);
  const [focusedCardIndex, setFocusedCardIndex] = React.useState(
    null,
  );

  const showModal = () => {
    try { sendGAEvent('event', 'showContactUsModal', { value: new Date(Date.now()).toISOString() }); }
    catch (e) { console.error(e); }
    mdlRef.current && mdlRef.current.showModal();
  }
  const handleFocus = (index) => {
    try { sendGAEvent('event', 'sectionFocusClicked', { index, value: cardData[index]?.title }); }
    catch (e) { console.error(e); }
    setFocusedCardIndex(index);
  };

  const handleBlur = () => {
    setFocusedCardIndex(null);
  };

  return (
    <>
      <Head>
        <title>Subanet - Welcome</title>
        <meta name="description" content="Subanet Limited is a UK based company that provides IT services and solutions to businesses and individuals." />
        <meta property="og:title" content="Subanet - Welcome" />
        <meta property="og:description" content="Subanet Limited is a UK based company that provides IT services and solutions to businesses and individuals." />
        <meta property="og:image" content="https://subanet.com/logo.svg" />
        <meta name="twitter:card" content="summary_large_image" />
      </Head>
      <main className={`${geistSans.variable} ${geistMono.variable} font-[family-name:var(--font-geist-sans)]`}>
        {/* Navigation */}
        <div
          className="fixed inset-0 -z-10"
          style={{
            // backgroundImage: 'url("/bg.jpg")',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            backgroundColor: 'rgba(255,255,255,0.95)',
          }}
        />
        <nav className="fixed w-full bg-white/70 backdrop-blur-sm shadow-sm z-50">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="text-2xl font-bold text-indigo-600"><Image
              src="/logo.svg"
              width={200}
              height={50}
              alt="Subanet Logo"
            /></div>
            <div className=" md:flex space-x-8">
              {/* <a href="#home" className="hover:text-indigo-600 text-sky-500">Home</a> */}
              <a href="#contact" onClick={showModal} className="hover:text-indigo-600 text-sky-500 ">
                <Typography variant={'body1'} sx={{ fontSize: { xs: '12px', md: '18px' } }}>Contact Us</Typography>
              </a>
            </div>
          </div>
        </nav>

        <section id="home" className="min-h-screen flex items-center pt-20">
          <div className="container mx-auto px-6 flex flex-col md:flex-row items-center">
            <div className="md:w-full space-y-8 mb-12">
              <div className="backdrop-blur-sm bg-white/30 p-2 rounded-lg my-4">
                <Card sx={{ p: 1, py: 1, mb: 0, borderWidth: 0, borderColor: '#ccc' }} className="bg-white/30 backdrop-blur-sm shadow-sm z-50">

                  {/* <Typography gutterBottom variant="caption" component="div">
              Prepare Your Digital Infrastructure for the Future
                          </Typography> */}
                  <Typography variant="h2" component="h2" className="text-gray-900 mb-2" sx={{ px: 2, fontSize: { xs: '32px', md: '54px' } }}>
                    Innovative digital solutions and sustainable growth strategies that drive your business into the future
                  </Typography>
                  {/* 
                <h1 className="text-4xl md:text-6xl font-bold text-gray-900">
                </h1> */}
                </Card>


                <Grid container spacing={2} columns={12}>

                  <Grid size={{ xs: 12, md: 12 }}>
                    <Typography variant='h4' component={'h4'} sx={{ mt: 4, pt: 2, py: 0, textAlign: 'center' }} >Services</Typography>

                    <Box sx={{ maxHeight: 10, overflow: 'hidden' }}>

                      <Wave fill='#efefef' fixll='#bababa'
                        paused={false}
                        style={{ display: 'flex' }}
                        options={{
                          height: 10,
                          amplitude: 20,
                          speed: 0.15,
                          points: 3
                        }}
                      />
                    </Box>

                  </Grid>

                  {cardData.map((card, index) => {
                    return (
                      <Grid key={index.toString()} size={{ xs: 12, md: 6 }}>
                        <SyledCard
                          // variant="outlined"
                          // sx={{ m: 2 }}
                          sx={{ p: 1, py: 2, mb: 2, flex: 1, borderWidth: 0.5, borderColor: '#ccc' }}
                          // className="bg-white/10 backdrop-blur-sm shadow-sm z-50"
                          onFocus={() => handleFocus(index)}
                          onMouseOver={() => handleFocus(index)}
                          onBlur={handleBlur}
                          tabIndex={index}
                          className={focusedCardIndex === index ? 'Mui-focused bg-white/90 backdrop-blur-sm shadow-sm z-50' : 'bg-white/50 backdrop-blur-sm shadow-sm z-50'}
                        >
                          <SyledCardContent>
                            <Typography gutterBottom variant="caption" component="div">
                              {cardData[index].tag}
                            </Typography>
                            <Typography variant="h4" component="h2" sx={{ fontSize: { xs: '18px', md: '32px' } }} className="text-gray-900 mb-2">
                              {cardData[index].title}
                            </Typography>
                            <Typography variant="body1" className="text-gray-800" sx={{ fontSize: '18px' }}>
                              {cardData[index].description}
                            </Typography>
                            <StyledTypography variant="body2" color="text.secondary" gutterBottom>
                              {cardData[index].descMore}
                            </StyledTypography>
                          </SyledCardContent>
                        </SyledCard>
                      </Grid>
                    )
                  })
                  }
                </Grid>

                <Box sx={{ textAlign: 'center' }}>
                  <AlertDialogSlide text={'Contact Us'} ref={mdlRef} />
                </Box>
              </div>
            </div>

            {/* <div className="md:w-1/2 mt-12 md:mt-0">
            <Image
              src="/hero-image.svg"
              alt="Hero"
              width={600}
              height={400}
              priority
              className="rounded-[5px] shadow-lg"
            />
          </div> */}
          </div>
        </section>

        <section id="home" className="flex items-center pt-20">
          <div className="container mx-auto px-6 flex flex-col md:flex-row items-center">
            <div className="md:w-full space-y-8 mb-12">
              <div className="backdrop-blur-sm bg-white/30 p-2 rounded-lg my-4">
                <Card sx={{ p: 1, py: 1, mb: 0, borderWidth: 0, borderColor: '#ccc' }} className="bg-white/30 backdrop-blur-sm shadow-sm z-50">

                  <Typography variant="h2" component="h2" className="text-gray-900 mb-2" sx={{ px: 2, fontSize: { xs: '18px', md: '24px' } }}>
                    Subanet Sandboxes
                  </Typography>
                  {/* 
                <h1 className="text-4xl md:text-6xl font-bold text-gray-900">
                </h1> */}
                </Card>


              </div>
              <SyledCard
                // variant="outlined"
                // sx={{ m: 2 }}
                sx={{ p: 1, py: 2, mb: 2, flex: 1, borderWidth: 0.5, borderColor: '#ccc' }}
                // className="bg-white/10 backdrop-blur-sm shadow-sm z-50"
                onFocus={() => handleFocus('sentiment')}
                onMouseOver={() => handleFocus('sentiment')}
                onBlur={handleBlur}
                tabIndex={'sentiment'}
                className={focusedCardIndex === 'sentiment' ? 'Mui-focused bg-white/90 backdrop-blur-sm shadow-sm z-50' : 'bg-white/50 backdrop-blur-sm shadow-sm z-50'}
              >
                <SyledCardContent>
                  <Typography gutterBottom variant="caption" component="div">
                    {'Sentiment analysis is the automated process of tagging data according to their sentiment, such as positive, negative and neutral. Sentiment analysis allows companies to analyze data at scale, detect insights and automate processes.'}
                  </Typography>
                  <Typography variant="h4" component="h2" sx={{ fontSize: { xs: '18px', md: '32px' } }} className="text-gray-900 mb-2">
                    {'#1 Sentiment Analysis'}
                  </Typography>
                  <Typography variant="body1" className="text-gray-800" sx={{ fontSize: '18px' }}>
                    {'Sentiment analysis is a natural language processing technique that identifies the polarity of a given text. There are different flavors of sentiment analysis, but one of the most widely used techniques labels data into positive, negative and neutral'}
                  </Typography>


                  <Typography variant="body2" className="text-gray-800">
                    Supports English plus Chinese (中文), Spanish (Español), Hindi (हिन्दी), Arabic (العربية), Bengali (বাংলা), Portuguese (Português), Russian (Русский), Japanese (日本語), German (Deutsch), Malay (Bahasa Melayu), Telugu (తెలుగు), Vietnamese (Tiếng Việt), Korean (한국어), French (Français), Turkish (Türkçe), Italian (Italiano), Polish (Polski), Ukrainian (Українська), Tagalog, Dutch (Nederlands), Swiss German (Schweizerdeutsch).

                  </Typography>

                  <Typography gutterBottom variant="caption" component="a" href="https://huggingface.co/distilbert/distilbert-base-multilingual-cased" target="_blank" rel="noreferrer">
                    Used Model: tabularisai/multilingual-sentiment-analysis | distilbert/distilbert-base-multilingual-cased
                  </Typography>

                  <Typography gutterBottom variant="caption" component="div" sx={{ mt: 2 }}>
                    {'Ideal for'}
                  </Typography>

                  <Stack sx={{ flexDirection: 'row', justifyContent: 'flex-start' }} variant="body2" className="text-gray-800">
                    <Box sx={{ flex: 1 }}>
                      <Typography>Social media analysis</Typography>
                      <Typography>Customer feedback analysis</Typography>
                      <Typography>Product reviews classification</Typography>
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography>Brand monitoring</Typography>
                      <Typography>Market research</Typography>
                      <Typography>Customer service optimization</Typography>
                      <Typography>Competitive intelligence</Typography>
                    </Box>
                  </Stack>

                  <StyledTypography variant="body2" color="text.secondary" gutterBottom>
                    <Link style={{ color: 'blue' }} title='Sentiment Analysis' href="/sandbox/sentiment">{'visit our sandbox to try out sentiment analysis'}</Link>
                    {/* {'visit our sandbox to try out sentiment analysis'} */}
                  </StyledTypography>
                </SyledCardContent>
              </SyledCard>

</div>
</div>
</section>
        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="container mx-auto px-6 text-center">
            <p>&copy; 1989-2025 Subanet. All rights reserved.</p>
          </div>
        </footer>
      </main>
    </>
  );
}

const cardData = [
  {
    img: 'https://picsum.photos/800/450?random=1',
    tag: 'Prepare Your Digital Infrastructure for the Future',
    title: 'Digital Transformation Solutions',
    description:
      'Transform your digital infrastructure, redesign business processes, and enhance efficiency.',
    descMore: 'We help transform your business processes and product offerings to meet the demands of the digital age. From migrating to modern ERP systems like SAP Hana to completely reengineering your workflows, we make your organization more agile and competitive.',
    authors: [
      { name: 'Remy Sharp', avatar: '/static/images/avatar/1.jpg' },
      { name: 'Travis Howard', avatar: '/static/images/avatar/2.jpg' },
    ],
  },
  {
    img: 'https://picsum.photos/800/450?random=2',
    tag: 'Automate Your Processes for Time and Cost Savings',
    title: 'Operational Efficiency & Automation',
    description:
      'Leverage Robotic Process Automation (RPA) to streamline your operations, saving time and reducing costs.',
    descMore: 'Optimize your operations and maximize workforce efficiency by automating repetitive tasks. Through Robotic Process Automation (RPA), we automate critical business processes, allowing your employees to focus on more strategic work.',
    authors: [{ name: 'Erica Johns', avatar: '/static/images/avatar/6.jpg' }],
  },
  {
    img: 'https://picsum.photos/800/450?random=3',
    tag: 'Strategically Manage and Develop Your Product Roadmap',
    title: 'Strategic Product Management & Development',
    description:
      'Align product roadmaps with corporate objectives to respond quickly to evolving market needs.',
    descMore: 'Align your product development process with corporate goals and market demands for a competitive advantage. We offer CRM systems and sales intelligence reporting to help you make data-driven decisions and boost product performance.',
    authors: [{ name: 'Kate Morrison', avatar: '/static/images/avatar/7.jpg' }],
  },
  {
    img: 'https://picsum.photos/800/450?random=4',
    tag: 'Reduce Costs with Sustainable Solutions',
    title: "Sustainability & Cost Reduction",
    description:
      "Reduce your carbon footprint with sustainable operational solutions.",
    descMore: 'Develop sustainable business processes that reduce both your environmental impact and operational costs. By transitioning workflows, we help your business significantly cut expenses.',
    authors: [{ name: 'Cindy Baker', avatar: '/static/images/avatar/3.jpg' }],
  },
];

const SyledCard = styled(Card)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  padding: 0,
  height: '100%',
  backgroundColor: (theme.vars || theme).palette.background.paper,
  '&:hover': {
    // backgroundColor: 'transparent',
    outline: '3px solid',
    // outlineColor: 'hsla(210, 98%, 48%, 0.5)',
    outlineColor: 'hsla(210, 98%, 48%, 0.2)',
    outlineOffset: '2px',
    cursor: 'pointer',
  },
  '&:focus-visible': {
    outline: '3px solid',
    outlineColor: 'hsla(210, 98%, 48%, 0.5)',
    outlineOffset: '2px',
  },
}));

const SyledCardContent = styled(CardContent)({
  display: 'flex',
  flexDirection: 'column',
  gap: 4,
  padding: 16,
  flexGrow: 1,
  '&:last-child': {
    paddingBottom: 16,
  },
});

const StyledTypography = styled(Typography)({
  display: '-webkit-box',
  WebkitBoxOrient: 'vertical',
  // WebkitLineClamp: 6,
  overflow: 'hidden',
  textOverflow: 'ellipsis',
});
