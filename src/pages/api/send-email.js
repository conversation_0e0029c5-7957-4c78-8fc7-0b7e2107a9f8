import nodemailer from 'nodemailer';
import { rateLimit } from 'express-rate-limit';
import Cors from 'cors';


// Initialize CORS middleware
const cors = Cors({
    origin: process.env.NEXT_PUBLIC_WEBSITE_URL, // your website domain
    methods: ['POST'],
});

// Rate limiting middleware
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 requests per windowMs
    validate: {
		xForwardedForHeader: false,
		default: true,
        ip: false,
	},
});

// Helper method to run middleware
const runMiddleware = (req, res, fn) => {
    return new Promise((resolve, reject) => {
        fn(req, res, (result) => {
            if (result instanceof Error) {
                return reject(result);
            }
            return resolve(result);
        });
    });
};

export default async function handler(req, res) {
    // Run CORS middleware
    // console.log('process.env', process.env)
    await runMiddleware(req, res, cors);

    // Check request method
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    // Verify request origin
    const origin = req.headers.origin;
    // if (origin !== process.env.NEXT_PUBLIC_WEBSITE_URL) {
    //     console.log('Forbidden origin:', origin);
    //     return res.status(403).json({ message: 'Forbidden' });
    // }

    if (![process.env.NEXT_PUBLIC_WEBSITE_URL, 'https://subanet.com', 'http://localhost:4000'].includes(origin) ) {
        console.log('Forbidden origin:', origin);
        return res.status(403).json({ message: 'Forbidden' });
    }


    // Verify API key
    const apiKey = req.headers['x-api-key'];
    if (apiKey !== 'SubanetContactForm') {
        console.log('Forbidden key:', apiKey);
        return res.status(401).json({ message: 'Unauthorized' });
    }

    // Apply rate limiting
    // try {
    //     await runMiddleware(req, res, limiter);
    // } catch (error) {
    //     console.log('Too many requests:', error);
    //     return res.status(429).json({ message: 'Too many requests' });
    // }

    //   if (req.method !== 'POST') {
    //     return res.status(405).json({ message: 'Method not allowed' });
    //   }

    const { fullName, email, message } = req.body;

    const auth = {
        user: process.env.YANDEX_EMAIL_ADDRESS,
        pass: process.env.YANDEX_EMAIL_PASSWORD,
    };

    var conf = {
        host: process.env.YANDEX_HOST, // Örneğin: 'pop.mail.server.com'
        port: process.env.YANDEX_HOST_PORT, // Güvenli bağlantı portu (SSL)
        secure: true,
        auth: {
            user: process.env.YANDEX_EMAIL_ADDRESS,
            pass: process.env.YANDEX_EMAIL_PASSWORD,
        },
    }
    const transporter = nodemailer.createTransport(conf);

    try {
        await transporter.sendMail({
            from: process.env.YANDEX_EMAIL_ADDRESS,
            to: process.env.YANDEX_EMAIL_ADDRESS_TO,
            subject: 'Subanet.com - New Contact Form Submission',
            text: `Name: ${fullName}\nEmail: ${email}`,
            html: `
        <p>${fullName} - ${email} </p>
        <p><strong>Message:</strong></p>
        <p>${message}</p>
      `,
        });

        res.status(200).json({ message: 'Email sent successfully' });
    } catch (error) {
        console.error('Error sending email:', error);
        res.status(500).json({ message: 'Error sending email' });
    }
}