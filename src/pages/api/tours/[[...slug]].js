import { GoogleGenerativeAI } from "@google/generative-ai";
import { parseTourPage } from '../../../../lib/fnx/parseTourPage';
import clientPromise from "../../../../lib/fnx/mongodb";
export default async function handler(req, res) {
    // Set CORS headers
    const allowedOrigins = ['https://www.tatilsepeti.com'];
    const origin = req.headers.origin;
    const dbConnMongo = await clientPromise;
    const db = dbConnMongo.db('subanet');
    // res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (allowedOrigins.includes(origin)) {
        res.setHeader('Access-Control-Allow-Origin', origin);
    } else {
        res.setHeader('Access-Control-Allow-Origin', '*'); // Fallback to allow all
    }

    // Handle OPTIONS method for CORS preflight
    if (req.method === 'OPTIONS') {
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        return res.status(200).end();
    }

    try {
        switch (req.method) {
            case 'GET':
                // Get the slug from query parameters
                const slug = req.query.slug || [];
                // Join the slug array with '/' if it's an array
                const path = Array.isArray(slug) ? slug.join('/') : slug;
                console.log('Requested path:', path);
                return res.status(200).json({
                    success: true,
                    data: slug
                });


            case 'POST':
                try {
                    const slug_post = req.query.slug || [];
                    const dt = Date.now();
                    var fnReq = Array.isArray(slug_post) ? slug_post[0] : null;
                    // console.log(' ');
                    // console.log(' ' + new Date(Date.now()).toISOString(), ' slug_post', slug_post, )
                    var availFn = ['parse', 'comparetours', 'reviewtours', 'reviewtourssimulation'];

                    if (availFn.includes(fnReq)) {
                        let resp = await fnPost[fnReq](req, res, db);
                        return res.status(200).json(resp);
                    } else {
                        console.log('ufo!')
                        return res.status(500).end('ufo!');
                    }
                } catch (e) {
                    console.log('epst', e);
                    return res.status(500).end(e.message);
                }

            default:
                res.setHeader('Allow', ['GET', 'POST']);
                return res.status(500).send(`Method ${req.method} Not Allowed`);
        }

    } catch (error) {
        console.error('API error:', error.message);
        return res.status(500).send(`Error: ${error.message}`);
    }
}
const fnPost = {
    parse: async (req, res, dbConn) => {
        return new Promise(async (resolve, reject) => {
            try {
                const pageExpiresInSec = 3000;
                const { item } = req.body;
                if (!item) reject('item is required');
                const dtBOP = Date.now();
                // Validate client and get schema
                const clientData = await fnDB.findClientData(item.clientID, dbConn);
                if (!clientData) {
                    reject('Invalid client ID: ' + item.clientID)
                }
                const { clientSchema, products, productConfigs } = clientData;

                if ((!products) || (Array.isArray(products) && !products.includes('tourComparison'))) {
                    reject('Invalid products: ')
                }

                const domain = new URL(item.parentUrl).hostname.replace(/^(.*?\.)?([^.]+(\.[^.]+)+)$/, '$2');
                const domainsParseble = productConfigs['tourComparison']['domainsParseble'];

                if ((!domainsParseble) || (Array.isArray(domainsParseble) && !domainsParseble.includes(domain))) {
                    reject('Invalid domain')
                }

                if (!clientSchema) {
                    reject('Client schema not found')
                }
                const existingTour = await fnDB.findTourData(item.turID, clientSchema, dbConn);
                const now = new Date();

                if (existingTour) {
                    const timeDiff = (now - existingTour.createdAt) / 1000;
                    if (timeDiff < pageExpiresInSec) {
                        return res.status(200).json({
                            success: true,
                            data: existingTour,
                            cached: true
                        });
                    }
                }
                // Delete old entries
                await fnDB.cleanOldData(pageExpiresInSec, clientSchema, dbConn);
                // Parse the tour page with client schema
                const parsedData = await parseTourPage(item.parentUrl, clientSchema);
                // console.log('Parsed Page', Date.now() - dtBOP);
                // Prepare data for MongoDB
                const tourDocument = {
                    itemId: item.itemId,
                    turID: item.turID,
                    title: item.title,
                    clientID: item.clientID,
                    clientSchema,
                    // products,
                    // productConfigs,
                    // sessionId: item.sessionId,
                    originalUrl: item.parentUrl,
                    imgSrc: item.imgSrc,
                    parsedContent: parsedData,
                    createdAt: now,
                };

                // Save to MongoDB with client schema
                await fnDB.saveTourData(tourDocument, clientSchema, dbConn);
                // console.log(' ' + new Date(Date.now()).toISOString(), ' Saved Page', Date.now() - dtBOP);

                // Log transaction before resolving
                try {
                    const transactionCollection = dbConn.collection('subanet.app.fact.transactions');
                    await transactionCollection.insertOne({
                        clientID: item.clientID,
                        clientSchema,
                        payload: {
                            originalUrl: item.parentUrl,
                            turID: item.turID,
                            title: item.title,
                        },
                        function: 'parseurl',
                        timestamp: new Date(),
                    });
                } catch (err) {
                    console.error('Transaction logging error:', err);
                }


                resolve({
                    success: true,
                    data: tourDocument,
                    cached: false
                })
                // return res.status(200).json({
                //     success: true,
                //     data: tourDocument,
                //     cached: false
                // });

            } catch (error) {
                console.error('parse error:', error);
                reject(error.message);
                // return res.status(500).json({
                //     success: false,
                //     error: error.message,
                //     cached: false
                // });
            }

        });
    },
    comparetours: async (req, res, dbConn) => {
        const pageExpiresInSec = 300;
        return new Promise(async (resolve, reject) => {
            try {
                const { tours, clientID } = req.body;
                if (!tours || !clientID) reject('tours required');
                const dtBOP = Date.now();
                const clientData = await fnDB.findClientData(clientID, dbConn);
                if (!clientData) {
                    reject('Invalid client ID: ' + clientID)
                }
                const { clientSchema, products, productConfigs } = clientData;
                if ((!products) || (Array.isArray(products) && !products.includes('tourComparison'))) {
                    reject('Invalid products: ')
                }

                if (!clientSchema) {
                    reject('Client schema not found')
                }
                // Delete old entries
                await fnDB.cleanOldData(pageExpiresInSec, clientSchema, dbConn);

                const tourIDs = tours.map(t => t.turID);
                const expirationDate = new Date(Date.now() - pageExpiresInSec * 1000);
                // console.log('expires @', expirationDate)
                const q = [
                    {
                        $match: {
                            "createdAt": { $gt: new Date(expirationDate) },
                            "clientSchema": clientSchema,
                            "turID": { "$in": tourIDs },
                        }
                    },
                    {
                        $sort: { "createdAt": -1 } // "createdAt" alanına göre azalan (en yüksekten en düşüğe) sıralama
                    },
                    {
                        $group: {
                            _id: "$turID", // Her turID için gruplandır
                            enYuksekCreatedAt: { $first: "$$ROOT" }
                        }
                    },
                    {
                        $replaceRoot: { newRoot: "$enYuksekCreatedAt" }
                    }
                ]
                const collectionName = `app.${clientSchema}.tourComparison.dim.tours`;
                const collection = dbConn.collection(collectionName);
                const recs = await collection.aggregate(q).toArray();
                let ToursToFetch = tours; //[]
                if (Array.isArray(recs) && recs.length !== 0) {
                    let IdsRequested = tours;
                    let IdsWeHave = recs.map(r => r.turID);
                    ToursToFetch = IdsRequested.filter(item => !IdsWeHave.includes(String(item.turID)));
                }
                // else {
                //     ToursToFetch = IdsRequested;
                // };
                if (ToursToFetch.length !== 0) {
                    for (const tf of ToursToFetch) {
                        // console.log('tf', tf);
                        const parsedData = await parseTourPage(tf.url, clientSchema);
                        const now = new Date();

                        const tourDocument = {
                            itemId: tf.itemID,
                            turID: tf.turID,
                            title: tf.title,
                            clientID: clientID,
                            clientSchema,
                            // products,
                            // productConfigs,
                            // sessionId: item.sessionId,
                            originalUrl: tf.url,
                            imgSrc: tf.imgSrc,
                            parsedContent: parsedData,
                            createdAt: now,
                        };
                        await fnDB.saveTourData(tourDocument, clientSchema, dbConn);

                        recs.push(tourDocument)
                    }
                }
                // console.log('query', JSON.stringify(q), recs, ToursToFetch)
                const toursAI = []
                recs.map(tourR => {
                    let tour = tourR.parsedContent;

                    let pStartPoint = '-';
                    try {
                        pStartPoint = tour.departurePoints[0];
                    } catch (e) { }


                    let pPrices = [];
                    try {
                        tour.prices.map(p => {
                            pPrices.push({
                                title: p.title,
                                price: p.prices?.doubleRoom?.tl,
                            })
                        })
                    } catch (e) { }


                    function findMinTlPrice(data) {
                        // Başlangıçta minimum fiyatı sonsuz olarak ayarla
                        let minPrice = Infinity;
                        let minItem = null; // En düşük fiyatı içeren objeyi sakla
                        Array.isArray(data) && data.map(item => {
                            const price = parseFloat(item.tl); // TL değerini sayıya çevir
                            if (!isNaN(price) && price < minPrice) {
                                minPrice = price;
                                minItem = item; // En düşük fiyatı içeren objeyi kaydet
                            }
                        })

                        // Eğer hiçbir öğe bulunamazsa null döndür
                        if (!minItem) {
                            return null;
                        }

                        // Minimum fiyatı içeren objeyi döndür
                        return minPrice;
                    }

                    let pFiyat = '-99';
                    try {
                        pFiyat = findMinTlPrice(tour.minPrice);
                    } catch (e) {
                        console.log('error', e.message)
                    }


                    let pTarih = '-';
                    try {
                        pTarih = tour.dates[0]['value'];
                    } catch (e) { }

                    let tourAI = {}
                    tourAI.tourID = tourR.turID;
                    tourAI.tourUrl = tourR.originalUrl;
                    // tourAI.agency = isAIanalysisRequested == 'ai' ? null : tour.ACENTA_ADI;
                    tourAI.tourPicture = tour.imgSrc
                    tourAI.tourType = Array.isArray(tour.tourType) ? tour.tourType.slice(1).map(item => item.title).join(" / ") : '-';
                    tourAI.tourTitle = tourR.title;
                    tourAI.tourName = tour.tourName;
                    tourAI.departureCity = pStartPoint;
                    tourAI.transportation = tour.transportation;
                    // tourAI.arrivalTransportation = tour.DONUS_ULASIM_ACIKLAMA;
                    tourAI.numberOfCities = Array.isArray(tour.cities) ? tour.cities.length : 0;
                    tourAI.cities = tour.cities;
                    tourAI.duration = Array.isArray(tour.itenary) ? tour.itenary.length : 0;
                    // tourAI.tags = tour.KATILIM_NOTU;
                    // tourAI.startingCity = tour.BASLANGIC_SEHIR_ADI;
                    tourAI.baseStartingPrice = pFiyat + ' TL';
                    tourAI.prices = tour.minPrice;
                    // tourAI.maxStartingPrice = parseInt(tour.MAX_FIYAT_S).toFixed(0) + ' TRY';
                    tourAI.includedInPackage = tour.includedServices;
                    tourAI.notIncludedInPackage = tour.notIncludedServices;
                    tourAI.itenary = tour.itenary;
                    tourAI.disclaimers = tour.description;
                    tourAI.firstDate = pTarih;
                    // tourAI.ILK_DONEM_TARIHI_S = tour.ILK_DONEM_TARIHI_S;
                    // tourAI.datesAndPrices = tour.TUR_TARIH_FIYAT_OZET;
                    toursAI.push(tourAI);
                })
                // let resp = { tourData: toursAI }
                // Check existing tour data with client schema
                console.log(' ' + new Date(Date.now()).toISOString(), ' Compare Page', Date.now() - dtBOP); //, tours, clientID

                // Log transaction before resolving
                try {
                    const transactionCollection = dbConn.collection('subanet.app.fact.transactions');
                    await transactionCollection.insertOne({
                        clientID,
                        clientSchema,
                        payload: { tours },
                        function: 'comparetours',
                        timestamp: new Date(),
                    });
                } catch (err) {
                    console.error('Transaction logging error:', err);
                }

                resolve({
                    success: true,
                    data: toursAI,
                    cached: false
                })

            } catch (error) {
                console.error('parse error:', error);
                reject(error.message);
            }

        });
    },

    reviewtourssimulation: (req, res, dbConn) => {

        return new Promise(async (resolve, reject) => {
            const transactionID = Math.random().toString(36).substr(2, 9);
            const pageExpiresInSec = 300;
            const dt = Date.now();
            let rBody;
            let aiSource = 'Gemini';
            let fnAI = aiSource == 'Gemini' ? GetGemini : GetRouter

            try {
                rBody = typeof req.body !== 'object' ? JSON.parse(req.body) : req.body;
            } catch (e) {
                console.log('error1', e.message);
            }

            if (rBody) {
                const { comparedata, clientID, sessionId } = rBody;
                if (!comparedata || !clientID) reject('tours required');
                const clientData = await fnDB.findClientData(clientID, dbConn);
                if (!clientData) {
                    reject('Invalid client ID: ' + clientID)
                }
                const { clientSchema, products, productConfigs = {} } = clientData;
                if ((!products) || (Array.isArray(products) && !products.includes('tourComparison'))) {
                    reject('Invalid products: ')
                }

                if (!clientSchema) {
                    reject('Client schema not found')
                }

                const { tourComparison = {} } = productConfigs;
                const { aiConfig = {} } = tourComparison;
                const { aiModel, modelMode, generationConfig } = aiConfig;
                const tourIDs = comparedata.map(t => t.turID);
                // const expirationDate = new Date(Date.now() - pageExpiresInSec * 1000);
                const q = [
                    {
                        $match: {
                            // "createdAt": { $gt: new Date(expirationDate) },
                            "clientSchema": clientSchema,
                            "turID": { "$in": tourIDs },
                        }
                    },
                    {
                        $sort: { "createdAt": -1 } // "createdAt" alanına göre azalan (en yüksekten en düşüğe) sıralama
                    },
                    {
                        $group: {
                            _id: "$turID", // Her turID için gruplandır
                            enYuksekCreatedAt: { $first: "$$ROOT" }
                        }
                    },
                    {
                        $replaceRoot: { newRoot: "$enYuksekCreatedAt" }
                    }
                ]

                const collectionName = `app.${clientSchema}.tourComparison.dim.tours`;
                const collection = dbConn.collection(collectionName);
                const toursStg = await collection.aggregate(q).toArray();
                const tours = toursStg.map(ts => {
                    return {
                        turID: ts.turID,
                        originalUrl: ts.originalUrl,
                        title: ts.title,
                        ...ts.parsedContent
                    }
                });
                tours.map(tourX => {
                    delete tourX.dates;
                    delete tourX.parsedAt;
                    delete tourX.greet;
                    delete tourX.tourType;
                });
                let qPre = fnPrompts.reviewtours({ payload: { tours } });
                try {
                    qPre = cleanHtmlTags(qPre);
                } catch (e) { }
                const modelNameCalc = aiSource == 'Gemini' ? (aiModel || 'gemini-2.0-flash') : "qwen/qwen-max";
                // console.log(new Date(), 'ai review baslatma süresi: ', modelNameCalc, Date.now() - dt)
                let qX = await fnAI({
                    modelName: modelNameCalc,
                    qPrompt: qPre,
                    modelConfig: generationConfig,
                    chatmode: modelMode == 'chat',
                });
                // console.log('prompt', qPre);
                // console.log('resp', JSON.stringify(qX));

                const collectionReviewName = `app.${clientSchema}.tourReviews.dim.tours`;
                try {
                    const collectionReview = dbConn.collection(collectionReviewName);
                    await collectionReview.insertOne({
                        transactionID,
                        tourIDs,
                        comparedata,
                        aiPrompt: qPre,
                        modelName: modelNameCalc,
                        aiReview: qX,
                        processTime: Date.now() - dt,
                        timestamp: new Date(),
                    });
                } catch (err) {
                    console.error('Error saving review to db:', err);
                }


                // Log transaction before resolving
                try {
                    const transactionCollection = dbConn.collection('subanet.app.fact.transactions');
                    await transactionCollection.insertOne({
                        clientID,
                        clientSchema,
                        payload: {
                            transactionID,
                            tourIDs, comparedata,
                            modelName: modelNameCalc,
                            processTime: Date.now() - dt,
                        },
                        function: 'reviewtours',
                        timestamp: new Date(),
                    });
                } catch (err) {
                    console.error('Transaction logging error:', err);
                }


                resolve({
                    success: true,
                    act: 'reviewtourssimulation',
                    payload: {
                        prompt: qPre,
                        comparedata,
                        clientID,
                    },
                    data: qX,
                    sure: Date.now() - dt
                })
            } else {
                reject('no role info')
            }
        });
    }
}
const fnDB = {
    saveTourData: async (data, clientSchema, dbConn) => {
        const collectionName = `app.${clientSchema}.tourComparison.dim.tours`;
        const collection = dbConn.collection(collectionName);
        return await collection.insertOne(data);
    },
    findTourData: async (turID, clientSchema, dbConn) => {
        const collectionName = `app.${clientSchema}.tourComparison.dim.tours`;
        const collection = dbConn.collection(collectionName);
        return await collection.findOne({ turID }, { sort: { createdAt: -1 } });
    },
    findClientData: async (clientID, dbConn) => {
        const dtBop = Date.now();
        const q = { clientID }
        let resp = await dbConn.collection('subanet.main.dim.customers').findOne(q);
        return resp

    },
    cleanOldData: async (pageExpiresInSec, clientSchema, dbConn) => {
        try {
            const collectionName = `app.${clientSchema}.tourComparison.dim.tours`;
            const collection = dbConn.collection(collectionName);
            const expirationDate = new Date(Date.now() - pageExpiresInSec * 1000);
            // console.log('expirationDate', expirationDate, collectionName)
            await collection.deleteMany({
                clientSchema: clientSchema,
                createdAt: { $lt: expirationDate }
            });
            return true;

        } catch (e) {
            console.log('clean old records error', e);
            return false;
        }
    }
}
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
function cleanHtmlTags(text) {
    // HTML etiketlerini temizleme
    const cleanedText = text.replace(/<[^>]*>/g, "");

    // Script içeriğini temizleme (gereksiz bir önlem olarak)
    const noScripts = cleanedText.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "");

    // İsteğe bağlı: Birden fazla boşluk bırakılan yerleri tek boşluğa indirgeme
    const finalText = noScripts.replace(/\s+/g, " ").trim();

    return finalText;
}
const GetRouter = async ({ modelName, qPrompt, showPrompt }) => {
    return new Promise(async (resolve, reject) => {
        const apiKey = process.env.OPENROUTER_API_KEY || "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";
        const dt = Date.now();
        const modelx = modelName || "google/gemini-2.0-flash-lite-preview-02-05:free";
        const model = modelName || "deepseek/deepseek-r1-zero:free";
        let resp = await fetch("https://openrouter.ai/api/v1/chat/completions", {
            method: "POST",
            headers: {
                Authorization: "Bearer " + apiKey,
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                model: model,
                messages: [
                    {
                        role: "user",
                        content: qPrompt || "What is the meaning of life?"
                    }
                ]
            })
        });
        let respJson = await resp.json();
        let respTxt = respJson.choices[0].message.content;

        const jsonRegex = /```json([\s\S]*?)```/g;
        const match = jsonRegex.exec(respTxt);
        var jsonData;
        if (match && match[1]) {
            try {
                // JSON stringini parse ediyoruz
                const jsonString = match[1].trim();
                jsonData = JSON.parse(jsonString);
                // Çıkarılan JSON verisini konsola yazdırıyoruz
                // console.log("Extracted JSON:", jsonData);
            } catch (error) {
                console.error("JSON parsing error:", error.message);
            }
        } else {
            console.error("No JSON found in the provided text.");
        }
        if (typeof jsonData === 'undefined') {
            jsonData = { error: true, errorDesc: 'Failed to extract JSON', modelResponse: respTxt };
        } else {
            jsonData = { ...jsonData, };
        }

        let respResult = {
            result: jsonData,
            modelNameAct: model,
            source: 'openRouter',
            sure: Date.now() - dt,
            posted_text: showPrompt ? qPrompt : null,
        };
        resolve(respResult);

    });
}

const GetGemini = async ({
    modelConfig = null, chatmode = true,
    modelName, qPrompt,
    respJson = true, showPrompt = false }) => {
    return new Promise(async (resolve, reject) => {
        try {
            function getRandomModel() {
                const models = [
                    'gemini-2.0-flash',
                    'gemini-2.0-flash-lite',
                    'gemini-2.0-flash-thinking-exp-01-21',
                    'gemini-2.0-pro-exp-02-05',
                    'gemini-2.5-pro-exp-03-25',
                    'gemini-1.5-flash-8b'
                ];

                // Ağırlıklar: model1 -> %40, model2 -> %30, model3 -> %30
                const weights = [0.45, 0.45, 0.05, 0.05];

                // Rastgele bir sayı oluştur (0 ile 1 arasında)
                const randomValue = Math.random();

                // Kümülatif toplam hesapla ve rastgele değeri kontrol et
                let cumulativeWeight = 0;
                for (let i = 0; i < weights.length; i++) {
                    cumulativeWeight += weights[i];
                    if (randomValue < cumulativeWeight) {
                        return models[i];
                    }
                }

                // Eğer bir hata olursa (teorik olarak mümkün değil), ilk elemanı döndür
                return models[0];
            }
            const dt = Date.now()
            const apiKey = process.env.TS_GEMINI_API_KEY;
            const genAI = new GoogleGenerativeAI(apiKey);
            const modalRandName = getRandomModel();
            const modalNameAct = modelName || modalRandName;
            // console.log('apiKey', apiKey, modalNameAct, modalRandName)
            const model = genAI.getGenerativeModel({
                model: modalNameAct,
            });

            const generationConfigU = {
                temperature: 1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192,
                responseMimeType: "text/plain",
            };
            const generationConfig = modelConfig || generationConfigU
            // const q = qText.toString().trim();
            const prompt = `
            What is the meaning of life
            `;
            const qqPrompt = qPrompt || prompt;
            var text;

            if (chatmode) {

                const chatSession = model.startChat({
                    generationConfig,
                    history: [
                    ],
                });
                const result = await chatSession.sendMessage(qqPrompt);
                text = result.response.text();
            } else {

                const result = await model.generateContent(
                    qqPrompt,
                    {
                        generationConfig,
                    }
                );
                const response = await result.response;
                text = response.text();
            }

            if (respJson) {
                const jsonRegex = /```json([\s\S]*?)```/g;
                const match = jsonRegex.exec(text);
                var jsonData;
                if (match && match[1]) {
                    try {
                        // JSON stringini parse ediyoruz
                        const jsonString = match[1].trim();
                        jsonData = JSON.parse(jsonString);
                        // Çıkarılan JSON verisini konsola yazdırıyoruz
                        // console.log("Extracted JSON:", jsonData);
                    } catch (error) {
                        console.error("JSON parsing error:", error.message);
                    }
                } else {
                    console.error("No JSON found in the provided text.");
                }
                if (typeof jsonData === 'undefined') {
                    // jsonData = { error: 'Failed to extract JSON' };
                    console.log(' error in parsing json', text);

                    jsonData = { error: true, errorDesc: 'Failed to extract JSON', modelResponse: text };
                } else {
                    // jsonData = { ...jsonData, posted_text: qPrompt, modalNameAct, sure: Date.now() - dt };
                    jsonData = { ...jsonData, };

                }

                let jsr = {
                    result: jsonData,
                    modelNameAct: modalNameAct,
                    source: 'geminiAI',
                    sure: Date.now() - dt,
                    posted_text: showPrompt ? qqPrompt : null,
                }

                resolve(jsr);
            } else {
                resolve(text)
            }
        }
        catch (error) {
            console.error("Error processing text:", error);
            resolve({ error: error });
        }
    });
}
const fnPrompts = {
    reviewtours: props => {
        const { payload = {} } = props;
        const { tours, lang = 'Turkish' } = payload;

        let prompt = `You are a travel expert AI assistant. Please analyze and compare these following tour packages in a clear, structured format. Create a detailed comparison focusing on key aspects that would help customers make an informed decision.
        `;
        Array.isArray(tours) & tours.length !== 0 && tours.map((t, ix) => {
            prompt += `TOUR ${ix + 1}: \n`;
            prompt += JSON.stringify(t);
            prompt += '\n\n';
        })
        prompt += `
                
        Please format your response as JSON with the following structure:

        {
            "comparison_title": "Tur Paketleri Karşılaştırmalı Analizi",
            "basic_info": {
                // Compare basic details like duration, price range, transportation
                "tour1": {
                    "tour_title": "",
                    "tour_startdate": "",
                    "agency": "",
                    "duration": "",
                    "price_range": "",
                    "transportation": ""
                },
                "tour2": {
                    "tour_title": "",
                    "tour_startdate": "",
                    "agency": "",
                    "duration": "",
                    "price_range": "",
                    "transportation": ""
                },
                "comparison": ""
            },
            "destinations": {
                // Compare visited cities and countries
                "tour1": {
                    "countries": "Bulgaristan, Sırbistan, İtalya, Yunanistan | 4 Ülke",
                    "cities": "Sofya, Belgrad, Ljubljana, Verona, Milano, Venedik, Floransa, Pisa, Siena, Roma, Igoumenitsa, Selanik | 12 Şehir"
                },
                "tour2": {
                    "countries": "Yunanistan",
                    "cities": "İpsala, Thassos Adası, Selanik, Halkidiki, Kavala"
                },
                "comparison": "İlk tur, 12 şehir ve 4 ülkeyi kapsayan zengin bir kültürel deneyim sunarken, ikinci tur 5 şehir ve yalnızca Yunanistan'ı içeriyor. Eğer birden fazla ülkeyi ve büyük şehirleri görmek istiyorsanız ilk tur sizin için daha uygun olabilir. Ancak, Yunanistan'ın sahil şeridini ve adalarını keşfetmek istiyorsanız ikinci tur daha ideal bir seçenek."
            },
            "price_value": {
                // Compare prices and what's included/excluded
                "tour1": {
                    "included": "",
                    "excluded": ""
                },
                "tour2": {
                    "included": "",
                    "excluded": ""
                    },
                "comparison": ""
            },
            "tour_highlights": {
                // Key differentiating features of each tour
                "tour1": {
                    "highlights": "",
                    "tags": ""
                },
                "tour2": {
                    "highlights": "",
                    "tags": ""
                },
                "comparison": ""
            },
            "ideal_for": {
                // What type of travelers each tour suits best
                "tour1": "",
                "tour2": ""
            },
            "pros_and_cons": {
                // List advantages and disadvantages of each tour
                "tour1": {
                    "pros": "List advantages of tour",
                    "cons": "List disadvantages of tour"
                },
                "tour2": {
                    "pros": "List advantages of tour",
                    "cons": "List disadvantages of tour"
                }
            },
            "final_verdict": {
                // Summary and recommendation based on different traveler preferences
                "recommendation": "..."
            }
        }

        Important comparison points to consider:
        1. Tour type and focus (cultural vs cruise)
        2. Duration and pace of travel. Focus on start date of tour.
        3. Number of countries and cities visited
        4. Included services and amenities
        5. Price per day value. 
        6. Price value for hotel location (hotel, hostel, motel)
        7. not included tour activities / sightseeing routes.
        8. Transportation methods
        9. Accommodation arrangements.
        10. Target audience suitability
        11. Physical activity level required
        12. Unique experiences offered
        13. ${lang} language should be used
        14. comparison will be used publicly on a website
        
        Make sure the comparison is objective, highlighting the unique aspects of each tour while helping potential customers understand which tour might better suit their preferences and needs. 

        `;
        return prompt;
    },
    generateRouteList: props => {
        const { payload = {} } = props;
        const { departureCity, departureTransportation, startCity, itenary, lang = 'Turkish' } = payload;
        let prompt = `
            You are a travel expert AI assistant. Please analyze tour Itenary and list cities and districts that are in the route. Sort them by time/day. Use departure city as starting point
        `;
        prompt += `Departure City:\n ${departureCity}: \n`;
        prompt += `Departure Transportation:\n ${departureTransportation}: \n`;
        prompt += `Starting City:\n ${startCity}: \n`;
        prompt += `Itenary:\n ${itenary}: \n`;
        prompt += '\n\n';
        prompt += `
        
Please format your response as JSON with the following structure:
 
{"routes": 
   [
    {
        "que": 1,
        "day": 0,
        "location": "Istanbul",
        "city": "Istanbul",
        "latlng": "41.015137, 28.979530",
        "notes": ""
    },

    {
        "que": 2,
        "day": 1,
        "location": "Adapazarı / Sakarya"
        "city": "Sakarya",
        "latlng": "41.015137, 28.979530",
        "notes": "included in package - muze adı - point of interests.."
    },

    {
        "que": 3,
        "day": 1,
        "location": "Gerede / Bolu",
        "city": "Bolu",
        "latlng": "41.015137, 28.979530"
    },
   ]
}

Important comparison points to consider:
1. Focus on start city of tour.
2. include only the cities and districts. add sub locations and optional route locations with extra cost in the notes key.
3. if city / district has optional route with extra cost, add it in the notes key.
4. if Transportation method mentioned, add it with new key 
5. ${lang} language should be used

Make sure the comparison is objective, highlighting the unique aspects of each tour while helping potential customers understand which tour might better suit their preferences and needs. 

        `;
        return prompt;
    },
};