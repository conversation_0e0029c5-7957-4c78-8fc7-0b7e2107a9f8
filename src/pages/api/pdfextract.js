import fs from 'fs';
import path from 'path';
import { IncomingForm } from "formidable"

export const config = {
    api: {
        bodyParser: false
    }
}

const handler = async (req, res) => {
    if (req.method === 'POST') {
        try {
            const uploadDir = process.cwd() + '/public/uploads';
            var file = {};
            let llmText = '';
            let fileName = '';
            let fileSize = '';
            let exportFormat = 'plain';
            const data = await new Promise((resolve, reject) => {
                const form = new IncomingForm({ multiples: false })
                form.parse(req, async (err, fields, files) => {
                    if (err) reject({ err })
                    // var extension = files.file?.originalFilename.split('.')
                    console.log('fields'    , fields);
                    if (fields && fields.format) {
                        exportFormat = fields.format;
                    }

                    var f
                    file = {
                        ...files.file[0],
                    };

                    fileSize = file.size;
                    fileName =  await saveFile(file);
                    const fileData = fs.readFileSync(file.filepath);
                    const formData = new FormData();
                    formData.append("format", exportFormat.toString() || "plain");
                    const blob = new Blob([fileData]);
                    formData.append('file', blob, fileName); 

                    try {
                        const response = await fetch('http://127.0.0.1:5000/extract-text', {
                            method: 'POST',
                            body: formData,
                            redirect: "follow"
                        });
                        const textData = await response.json();
                        llmText = textData.text;
                    } catch (error) {
                        console.error('Error extracting text:', error);
                    }
                    resolve({ files })
                })
            })

            res.status(200).json({
                exportFormat: exportFormat.toString(),
                pdfText: llmText,
                fileName: fileName?.fileName,  
                fileSize,              
            })

        } catch (error) {
            console.error('Error uploading files:', error);
            res.status(500).json({ error: 'Failed to upload files' });
        }
    } else {
        return res.status(405).json("Method not allowed for File Upload")
    }
};

export default handler;


const saveFile = async (file, useOriginalName = true, useOneFile = false,) => {
    const basedir = path.resolve(process.cwd());
    const basePath = basedir + '/public/uploads'
    return new Promise(async (resolve, reject) => {
        try {
            var extension = file.originalFilename.split('.');
            var fileSize = file.size;
            // console.log('fileSize', fileSize);
            if (fileSize && fileSize < 5000000) {
                const fileName = (useOneFile ? 'ff' : ((useOriginalName ? extension[0] : file.newFilename))); // + '_' + Date.now().toString()
                var file2s = basePath + '/' + fileName;
                if (Array.isArray(extension)) file2s += '.' + extension[1]
                const data = fs.readFileSync(file.filepath);
                fs.writeFile(file2s, data, (err) => {
                    if (err) reject(err);
                    resolve({
                        fileName: fileName + (extension.length > 1 ? '.' + extension[1] : ''),
                        filePath: file2s,
                    });
                });
            } else {
                reject('File size is too large');
                return;
            }
        } catch (e) {
            reject(e)
        }
    })
};