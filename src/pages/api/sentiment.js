import Cors from 'cors';
import axios from 'axios';
import badWords from '../../../lib/curse_words';
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";

import clientPromise from "../../../lib/fnx/mongodb";

const useAIClouseService = true;

// Initialize CORS middleware
const cors = Cors({
    origin: process.env.NEXT_PUBLIC_WEBSITE_URL, // your website domain
    methods: ['POST'],
});

// Helper method to run middleware
const runMiddleware = (req, res, fn) => {
    return new Promise((resolve, reject) => {
        fn(req, res, (result) => {
            if (result instanceof Error) {
                return reject(result);
            }
            return resolve(result);
        });
    });
};

const API_URL = 'http://127.0.0.1:8000/predict'; // Replace with your actual API endpoint

const timestamp = exports.timestamp = () => `[${new Date().toUTCString()}]`
const log = exports.log = (...args) => console.log(timestamp(), ...args)

async function analyzeSentiment(text) {
    let data = JSON.stringify({
        "texts": [
            text.toString().trim(),
        ]
    });

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: API_URL,
        headers: {
            'x-api-key': 'taner',
            'x-secret-key': 'subasi',
            'Content-Type': 'application/json'
        },
        data: data
    };

    try {
        const response = await axios.request(config);
        // console.log('resp', JSON.stringify(response.data));
        return response.data;
    } catch (error) {
        log(error);
        throw error;
    }
}

export default async function handler(req, res) {

    await runMiddleware(req, res, cors);
    // Check request method
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    // Connect to the MongoDB cluster
    const dbConnMongo = await clientPromise;

    // Verify API key 
    const apiTopKey = req.headers['x-api-topkey'];
    const origin = req.headers.origin;
    if (apiTopKey !== 'tatilsepeti') {
        // Verify request origin
        if (![process.env.NEXT_PUBLIC_WEBSITE_URL, 'https://subanet.com', 'http://localhost:4000'].includes(origin)) {
            log('Forbidden origin:', origin);
            return res.status(403).json({ message: 'Forbidden origin' });
        }
        // Verify API key 
        const apiKey = req.headers['x-api-key'];
        if (apiKey !== 'SubanetSandboxSentimentAnaylsis') {
            log('Forbidden key:', apiKey);
            return res.status(401).json({ message: 'Unauthorized' });
        }
    }

    let useGemini = false;
    if (apiTopKey == 'tatilsepeti' && getRandomBoolean()) {
        useGemini = true;
    };
    useGemini = useAIClouseService ? true : useGemini;
    const { texts } = req.body;
    if (!useGemini) {

        try {
            const results = await analyzeSentiment(texts);
            let sTrack = {};
            let reviewNeeded = false;
            if (texts && badWords) {
                // const words = texts.toLowerCase().split(/\s+/);
                // reviewNeeded = words.some(word => badWords.includes(word.toLowerCase()));
                const words = texts.toLowerCase().replace(/[^a-zA-Z0-9\s]/g, '').split(/\s+/);
                // reviewNeeded = words.some(word => badWords.some(badWord => badWord.replace(/[^a-zA-Z0-9\s]/g, '').trim() === word));
    
                const matchedWords = words.filter(word => badWords.some(badWord => badWord.replace(/[^a-zA-Z0-9\s]/g, '').trim() === word));
                if (matchedWords.length > 0) {
                    results.badWords = matchedWords;
                    // log('Matched bad words:', matchedWords);
                }
                reviewNeeded = matchedWords.length > 0;
    
            }
            // console.log('reviewNeeded', reviewNeeded);
            sTrack.reviewNeeded = reviewNeeded;
            sTrack.modalName = 'local';
    
            if (dbConnMongo && results) {
                results.modalName = 'local';
                sTrack.text2analyze = texts;
                sTrack.origin = origin;
                try {
                    const ip = req.headers['x-forwarded-for'];
                    sTrack.ip = ip;
                } catch (eC) { }
                
                try {
                    const db = dbConnMongo.db('subanet')
                    const coll = db.collection('fact.sentiments');
                    results.reviewNeeded = reviewNeeded;
                    sTrack.rsgID = generateKey({ inclTime: false });
                    sTrack.dtupdated = new Date(Date.now()).toISOString();
                    sTrack.dtcreated = new Date(Date.now()).toISOString();
                    sTrack = {
                        ...sTrack,
                        ...results,
                    }
                    try {
                        await coll.insertOne(sTrack);
                    } catch (err) {
                        log('err', err);
                    }
                } catch (eX) {
                    log('err', eX);
                }
            } else {
                log('no Mongo')
            } 

            res.json(results || { resp: 'No data' });
        } catch (error) {
            //log('Error in analysis:', error);
            let resp = {"sentiments":[{"sentiment_text_language":["tr"],"sentiment_value":"Neutral","probabilities":[{"sentiment":"Very Negative","score":0.00},{"sentiment":"Negative","score":0.00},{"sentiment":"Neutral","score":0.00},{"sentiment":"Positive","score":0.00},{"sentiment":"Very Positive","score":0.00}],"posted_text":"" + error.message, "modalNameAct":"local","sure":1549}],"sentiment_map":{"0":"Very Negative","1":"Negative","2":"Neutral","3":"Positive","4":"Very Positive"},"modalName":"Local","reviewNeeded":true}
            //res.status(500).json({ message: 'Error in analysis' });
            res.status(500).json(resp);
        }
    } else {   
        const results = await GetGeminiSentiment({ qText: texts, modelName: false });

        let sTrack = {};
        let reviewNeeded = false;
        if (texts && badWords) {
            const words = texts.toLowerCase().replace(/[^a-zA-Z0-9\s]/g, '').split(/\s+/);
            const matchedWords = words.filter(word => badWords.some(badWord => badWord.replace(/[^a-zA-Z0-9\s]/g, '').trim() === word));
            if (matchedWords.length > 0) {
                results.badWords = matchedWords;
            }
            reviewNeeded = matchedWords.length > 0;
        }
        sTrack.reviewNeeded = reviewNeeded;
        sTrack.modalName = 'local';

        if (dbConnMongo && results) {
            results.modalName = 'Google Gemini';
            sTrack.text2analyze = texts;
            sTrack.origin = origin;
            try {
                const ip = req.headers['x-forwarded-for'];
                sTrack.ip = ip;
            } catch (eC) { }
            
            try {
                const db = dbConnMongo.db('subanet')
                const coll = db.collection('fact.sentiments');
                results.reviewNeeded = reviewNeeded;
                sTrack.rsgID = generateKey({ inclTime: false });
                sTrack.dtupdated = new Date(Date.now()).toISOString();
                sTrack.dtcreated = new Date(Date.now()).toISOString();
                sTrack = {
                    ...sTrack,
                    ...results,
                }
                try {
                    await coll.insertOne(sTrack);
                } catch (err) {
                    log('err', err);
                }
            } catch (eX) {
                log('err', eX);
            }
        } else {
            log('no Mongo')
        } 

        res.json(results || { resp: 'No data' });
    }
}

const generateKey = ({ inclTime = false }) => {
    let randStr = (+new Date * Math.random()).toString(36).substring(0, 6);
    let resp = inclTime ? Date.now().toString() + '-' + randStr : randStr;
    return resp
};

function getRandomBoolean() {
    // Generate a random number between 0 and 1
    const randomValue = Math.random();
    // Return true if the random value is less than 0.7 (70% chance)
    return randomValue < 0.7;
  }

  
function getRandomModel() {
    const models = [
        'gemini-2.5-flash', 
        'gemini-2.5-flash-lite',
        // 'gemini-2.0-pro-exp-02-05',
        // 'gemini-2.0-flash-thinking-exp-01-21',
        // 'learnlm-1.5-pro-experimental'
    ];
    
    // Ağırlıklar: model1 -> %40, model2 -> %30, model3 -> %30
    const weights = [0.45, 0.55];
  
    // Rastgele bir sayı oluştur (0 ile 1 arasında)
    const randomValue = Math.random();
  
    // Kümülatif toplam hesapla ve rastgele değeri kontrol et
    let cumulativeWeight = 0;
    for (let i = 0; i < weights.length; i++) {
      cumulativeWeight += weights[i];
      if (randomValue < cumulativeWeight) {
        return models[i];
      }
    }
  
    // Eğer bir hata olursa (teorik olarak mümkün değil), ilk elemanı döndür
    return models[0];
  }

const GetGeminiSentiment = async ({ qText = 'Hello World', modelName}) => {
    return new Promise(async (resolve, reject) => {
        try {

            const dt = Date.now()
            const apiKey = process.env.TS_GEMINI_API_KEY;
            const genAI = new GoogleGenerativeAI(apiKey);

            const modalRandName = getRandomModel();
            // const modalNameAct = modelName || modalRandName;
            const modalNameAct = modelName || modalRandName;'gemini-2.5-flash-lite';
            //console.log('Model Name:', modalNameAct);
            const model = genAI.getGenerativeModel({
                model: modalNameAct,
            });

            const generationConfig = {
                temperature: 1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192,
                responseMimeType: "text/plain",
            };

            const chatSession = model.startChat({
                generationConfig,
                history: [
                ],
            });

            const q = qText.toString().trim();
            const prompt = `
            You are a sentiment analyzer for a online travel agency, and extracting sentiment score from customer comments.
            Please generate a valid JSON response following these strict rules:
            1. expected to compute sentiment with 5 scales, ["Very Positive", "Positive", "Neutral", "Negative", "Very Negative"].
            2. sentiment_text_language should be array of languages that the sentiment is computed.
            3. sentiment_value should be the most dominant sentiment value from the given text.
            4. Use the following sentiment scale: ["Very Positive", "Positive", "Neutral", "Negative", "Very Negative"]
            5. format of the response should be like below: 
                    {{
                        "sentiment_text_language": ["en", "tr", "fr", "de", "es"],
                        "sentiment_value": "Very Positive",
                        "probabilities": [
                            {{
                            "sentiment": "Very Negative",
                            "score": 0.10316821187734604
                            }},
                            {{
                            "sentiment": "Negative",
                            "score": 0.07875534892082214
                            }},
                            {{
                            "sentiment": "Neutral",
                            "score": 0.17881953716278076
                            }},
                            {{
                            "sentiment": "Positive",
                            "score": 0.27443355321884155
                            }},
                            {{
                            "sentiment": "Very Positive",
                            "score": 0.3648233413696289
                            }}
                            ]
                        }}
            6. Calculate probabilities for each sentiment value.
            7. If probabilities are not computed, return 0.0 for all probabilities.
            8. Total sum of the probabilities should be 1.0.
            9. Do not include any additional information or formatting beyond the requested JSON object.
            10. Do not include any other text or comments beyond the requested JSON object.
            Here is the text to analyze: ${q}
    `;
            const result = await chatSession.sendMessage(prompt);
            var text = result.response.text();
            const jsonRegex = /```json([\s\S]*?)```/g;
            const match = jsonRegex.exec(text);
            var jsonData = {};
            if (match && match[1]) {
                try {
                    // JSON stringini parse ediyoruz
                    const jsonString = match[1].trim();
                    jsonData = JSON.parse(jsonString);
                    // Çıkarılan JSON verisini konsola yazdırıyoruz
                    // console.log("Extracted JSON:", jsonData);
                } catch (error) {
                    console.error("JSON parsing error:", error.message);
                }
            } else {
                console.error("No JSON found in the provided text.");
            }
            if (typeof jsonData === 'undefined') {
                jsonData = { error: 'Failed to extract JSON' };
            } else {
                jsonData = { ...jsonData, posted_text: q, modalNameAct, sure: Date.now() - dt };
            }

            let jsr = {
                sentiments: [jsonData],
                "sentiment_map": {
                    "0": "Very Negative",
                    "1": "Negative",
                    "2": "Neutral",
                    "3": "Positive",
                    "4": "Very Positive"
                }
            }

            resolve(jsr);
        }
        catch (error) {
            console.error("Error processing text:", error);
            resolve({ error: error.message });
        }
    });
}
