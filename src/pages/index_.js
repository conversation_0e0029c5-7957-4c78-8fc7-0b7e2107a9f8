import Image from "next/image";
import localFont from "next/font/local";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export default function Home() {
  return (
    <main className={`${geistSans.variable} ${geistMono.variable} font-[family-name:var(--font-geist-sans)]`}>
      {/* Navigation */}
        <div
          className="fixed inset-0 -z-10"
          style={{
            backgroundImage: 'url("/bg.jpg")',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        />
        <nav className="fixed w-full bg-white/80 backdrop-blur-sm shadow-sm z-50">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="text-2xl font-bold text-indigo-600"><Image
          src="/logo.svg"
          width={300}
          height={50}
          alt="Subanet Logo"
            /></div>
            <div className="hidden md:flex space-x-8">
          <a href="#home" className="hover:text-indigo-600">Home</a>
          <a href="#services" className="hover:text-indigo-600">Services</a>
          <a href="#portfolio" className="hover:text-indigo-600">Portfolio</a>
          <a href="#contact" className="hover:text-indigo-600">Contact</a>
            </div>
          </div>
        </nav>

          <section id="home" className="min-h-screen flex items-center pt-20">
            <div className="container mx-auto px-6 flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 space-y-8">
            <div className="backdrop-blur-sm bg-white/30 p-6 rounded-lg">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900">
            Transform Your Business With Digital Solutions
              </h1>
              <p className="text-xl text-gray-800 mt-4">
            Expert mobile app development and data analytics services to drive your success
              </p>
              <button className="bg-indigo-600 text-white px-8 py-3 rounded-lg hover:bg-indigo-700 mt-6">
            Get Started
              </button>
            </div>
          </div>
          {/* <div className="md:w-1/2 mt-12 md:mt-0">
            <Image
              src="/hero-image.svg"
              alt="Hero"
              width={600}
              height={400}
              priority
              className="rounded-[5px] shadow-lg"
            />
          </div> */}
            </div>
          </section>

          {/* Services Section */}
          <section id="services" className="py-20 bg-gray-50">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-bold text-center mb-16">Our Services</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div key={index} className="bg-white p-8 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <h3 className="text-xl font-bold mb-4">{service.title}</h3>
              <p className="text-gray-600">{service.description}</p>
            </div>
          ))}
            </div>
          </div>
          </section>

          {/* Portfolio Section */}
      <section id="portfolio" className="py-20">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-16">Our Work</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project, index) => (
              <div key={index} className="group relative overflow-hidden rounded-lg">
                <Image
                  src={project.image}
                  alt={project.title}
                  width={400}
                  height={300}
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="text-white text-center p-4">
                    <h3 className="text-xl font-bold">{project.title}</h3>
                    <p>{project.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-16">Contact Us</h2>
          <div className="max-w-2xl mx-auto">
            <form className="space-y-6">
              <div>
                <input
                  type="text"
                  placeholder="Name"
                  className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-indigo-600"
                />
              </div>
              <div>
                <input
                  type="email"
                  placeholder="Email"
                  className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-indigo-600"
                />
              </div>
              <div>
                <textarea
                  placeholder="Message"
                  rows={5}
                  className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-indigo-600"
                ></textarea>
              </div>
              <button
                type="submit"
                className="w-full bg-indigo-600 text-white px-8 py-3 rounded-lg hover:bg-indigo-700"
              >
                Send Message
              </button>
            </form>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-6 text-center">
          <p>&copy; 2023 Subanet. All rights reserved.</p>
        </div>
      </footer>
    </main>
  );
}

const services = [
  {
    title: "Legacy Sistem Modernizasyonu",
    description: "AS400 gibi eski sistemlerin bulut tabanlı veya daha modern teknolojilere geçişine yönelik danışmanlık ve uygulama hizmetleri."
  },
  {
    title: "ERP ve CRM Uygulamaları",
    description: "SAP gibi entegre iş yazılımları, müşteri ilişkileri yönetimi çözümleri ve bunların kurulumu, entegrasyonu ve optimizasyonu."
  },
  {
    title: "RPA (Robotic Process Automation) Uygulamaları",
    description: "İş süreçlerinin otomasyonu için RPA yazılımları ve danışmanlık hizmetleri."
  },
  {
    title: "Dijital Pazarlama ve Satış Kanalları",
    description: "E-ticaret platformları, mobil uygulamalar, sosyal medya entegrasyonu, online satış kanalları kurulumu ve yönetimi."
  },
  {
    title: "Tedarik Zinciri Optimizasyonu",
    description: "Üretim ve dağıtım süreçlerinin iyileştirilmesi, envanter yönetimi ve lojistik çözümleri."
  },
  {
    title: "İş Süreçleri Analizi ve Tasarımı",
    description: "İşletmelerin mevcut süreçlerinin değerlendirilmesi, iyileştirme önerileri ve yeni süreçlerin tasarlanması."
  },
  {
    title: "Data Analitiği ve Raporlama",
    description: "Veri toplama, analiz ve raporlama araçları ile iş performansının izlenmesi ve karar destek sistemleri."
  },
  {
    title: "Müşteri Memnuniyeti Programları",
    description: "Müşteri geri bildirimlerinin toplanması ve analiz edilmesi, müşteri deneyimini iyileştirmeye yönelik stratejiler ve uygulamalar."
  },
  {
    title: "Müşteri Hizmetleri Kanalları",
    description: "Telefon, e-posta, canlı destek gibi müşteri hizmetleri kanallarının geliştirilmesi ve optimizasyonu."
  }
];

const projects = [
  {
    title: "E-commerce App",
    description: "Mobile shopping platform",
    image: "/project1.jpg"
  },
  {
    title: "Analytics Dashboard",
    description: "Data visualization platform",
    image: "/project2.jpg"
  },
  {
    title: "FinTech Solution",
    description: "Banking mobile app",
    image: "/project3.jpg"
  }
];