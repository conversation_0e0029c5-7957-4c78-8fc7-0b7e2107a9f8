{"name": "sn-subanet", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4000", "build": "next build", "start": "next start -p 4000", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@google/generative-ai": "^0.21.0", "@mui/icons-material": "latest", "@mui/lab": "^6.0.0-beta.22", "@mui/material": "^6.3.1", "@mui/material-nextjs": "^6.3.1", "@mui/styled-engine-sc": "^6.3.1", "@mui/styles": "^6.3.1", "@next/third-parties": "^15.1.3", "axios": "^1.8.4", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "formidable": "^3.5.2", "fs": "^0.0.1-security", "mixpanel": "^0.18.1", "mongodb": "^6.12.0", "next": "15.0.3", "nodemailer": "^6.9.16", "path": "^0.12.7", "react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106", "react-dropzone": "^14.3.5", "react-wavify": "^1.11.1"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1"}}