mongodb Query:

//db.getCollection('app.ttlspt.tourReviews.dim.tours').find({}).sort({_id: -1}).limit(3)
//db.getCollection('app.ttlspt.tourReviews.dim.tours').find({}).count()
db.getCollection('app.ttlspt.tourReviews.dim.tours').aggregate([
  {
    // 1. Aşama: Filtreleme
    $match: {
      "comparedata": { $exists: true, $size: 2 },
      "timestamp": { $exists: true }
    }
  },
  {
    // 2. Aşama: Gerekli alanları seç/yeniden adlandır (_id'yi koru)
    $project: {
      // _id alanı timestamp ve sıralama için örtülü olarak korunur
      tur1_baslik: { $arrayElemAt: ["$comparedata.title", 0] },
      tur2_baslik: { $arrayElemAt: ["$comparedata.title", 1] },
      processTime: 1, timestamp: 1
    }
  },
  {
    // 3. Aşama: Timestamp'e göre tersten sırala (en yeni en üste)
    $sort: {
      _id: -1 // ObjectId'ye göre tersten sıralama (en yeni belge önce gelir)
    }
  },
  {
    // 4. Aşama: Sıra numarası ekle (MongoDB 5.0+ Gerekli)
     $setWindowFields: {
        partitionBy: null,
        sortBy: { _id: -1 }, // Numaralandırma için _id'ye göre tersten sıralamayı kullan
        output: {
           sira_no: { $documentNumber: {} } // Sıra numarasını (1'den başlayarak) ata (artık en yeni belge 1 numara olacak)
        }
     }
  },
  {
    // 5. Aşama: Son çıktı formatı, timestamp formatlama ve _id'yi kaldırma
    $project: {
      _id: 0, // Orijinal _id'yi kaldır
      sira_no: 1,
      tur1_baslik: 1,
      tur2_baslik: 1,
      processTime: 1,
      timestamp: {
        $dateToString: {
          format: "%H:%M:%S",
          date: { $toDate: "$timestamp" },
          timezone: "Europe/Istanbul"
        }
      }
    }
  }
])
  

db.getCollection('app.ttlspt.tourReviews.dim.tours').aggregate([
  {
    // Opsiyonel ama önerilen: comparedata'nın var olduğundan ve tam 2 öğe içerdiğinden emin olun
    $match: {
      "comparedata": { $exists: true, $size: 2 }
    }
  },
  {
    $project: {
      _id: 0, // _id alanını sonuçtan çıkar
      karsilastirilan_ikili: [
         // comparedata dizisinin ilk öğesindeki title alanını al
        { $arrayElemAt: ["$comparedata.title", 0] },
         // comparedata dizisinin ikinci öğesindeki title alanını al
        { $arrayElemAt: ["$comparedata.title", 1] }
      ]
      /* Alternatif olarak ayrı alanlarda isterseniz:
      tur1: { $arrayElemAt: ["$comparedata.title", 0] },
      tur2: { $arrayElemAt: ["$comparedata.title", 1] }
      */
    }
  }
])
  




db.getCollection('app.ttlspt.tourReviews.dim.tours').aggregate([
  {
    // 1. Aşama: 'timestamp' alanı var olan ve Date tipinde olan belgeleri filtrele
    $match: {
      timestamp: { $exists: true, $type: "date" }
      // Eğer timestamp string ise: $match: { timestamp: { $exists: true, $type: "string" } }
      // Eğer timestamp number ise: $match: { timestamp: { $exists: true, $type: "number" } }
    }
  },
  {
    // 2. Aşama: 'timestamp' alanının gün bazında grupla ve say
    $group: {
      _id: {
        // MongoDB 5.0+ için $dateTrunc kullanımı önerilir:
        $dateTrunc: {
          date: "$timestamp",        // Tarih alanı
          unit: "day",              // Gün bazında grupla
          timezone: "Europe/Istanbul" // İstanbul saat dilimine göre gün başlangıcını belirle
        }
        // MongoDB 5.0 öncesi için alternatif:
        /*
        $dateToString: {
            format: "%Y-%m-%d",       // Yıl-Ay-Gün formatı
            date: "$timestamp",
            timezone: "Europe/Istanbul"
        }
        */
        // Eğer timestamp string ise ve formatı biliniyorsa $dateFromString kullanmak gerekir:
        /*
        $dateTrunc: {
            date: { $dateFromString: { dateString: "$timestamp", format: "<tarih_formati>", timezone: "Europe/Istanbul" } },
            unit: "day",
            timezone: "Europe/Istanbul"
        }
        */
        // Eğer timestamp Unix (sayısal) ise $toDate kullanmak gerekir:
        /*
        $dateTrunc: {
            date: { $toDate: "$timestamp" }, // Unix timestamp (milisaniye) ise
            // date: { $toDate: { $multiply: [ "$timestamp", 1000 ] } }, // Unix timestamp (saniye) ise
            unit: "day",
            timezone: "Europe/Istanbul"
        }
        */
      },
      // Her grup (gün) için belge sayısını topla
      karsilastirma_sayisi: { $sum: 1 }
    }
  },
  {
    // 3. Aşama: Sonuçları tarihe göre sırala (eskiden yeniye)
    $sort: {
      _id: 1 // Gruplama anahtarına (_id, yani tarihe) göre sırala
    }
  },
  {
    // 4. Aşama: Çıktıyı düzenle
    $project: {
      _id: 0, // Varsayılan _id'yi kaldır
      tarih: {
          // Eğer $dateTrunc kullanıldıysa _id Date objesidir, string'e çevir:
          $dateToString: {
              format: "%Y-%m-%d",       // YYYY-MM-DD formatı
              date: "$_id",
              timezone: "Europe/Istanbul" // Doğru saat dilimiyle string'e çevir
          }
          // Eğer gruplamada $dateToString kullanıldıysa _id zaten string'dir:
          // "$_id"
      },
      karsilastirma_sayisi: 1 // Hesaplanan sayıyı dahil et
    }
  }
])


--ozet gunluk.
db.getCollection('subanet.app.fact.transactions').aggregate([
    {
        // Yıl, ay, gün bilgilerini çıkar ve birleştir
        $project: {
            function: 1,
            dateKey: {
                $dateToString: {
                    format: "%Y-%m-%d",
                    date: "$timestamp"
                }
            }
        }
    },
    {
        // function ve dateKey bazında grupla ve say
        $group: {
            _id: {
                function: "$function",
                dateKey: "$dateKey"
            },
            count: { $sum: 1 }
        }
    },
    {
        // Dinamik anahtarlar oluştur (function değerleri ayrı anahtarlar olacak)
        $group: {
            _id: "$_id.dateKey", // Tarih anahtarı
            functions: {
                $push: {
                    k: "$_id.function", // Anahtar adı
                    v: "$count"         // Değer
                }
            }
        }
    },
    {
        // Dinamik anahtarları aç (function değerlerini ayrı anahtarlar haline getir)
        $replaceRoot: {
            newRoot: {
                $mergeObjects: [
                    { date: "$_id" }, // Tarih anahtarı
                    { $arrayToObject: "$functions" } // Function değerlerini aç
                ]
            }
        }
    },
    {
        // Sonuçları tarih sırasına göre sırala
        $sort: {
            date: 1
        }
    }
])
    