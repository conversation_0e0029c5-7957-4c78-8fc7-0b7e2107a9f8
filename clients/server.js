const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const Mixpanel = require('mixpanel');
const defaultScript = require(path.join(__dirname, 'injects', '67b75b1b6579206a6276c3d0.js'));
const app = express();
const PORT = 4021;

require('dotenv').config();
const environment = process.env.NODE_ENV || 'development';
console.log('Environment:', environment);

// Mixpanel initialization
const mixpanel = Mixpanel.init(process.env.MIXPANEL_TOKEN || '0051593911ecdfddc782d28b88b02f09');

// Tracking helper function
const trackEvent = (event, properties) => {
    if (environment !== 'development') {
        mixpanel.track(event, {
            ...properties,
            environment: environment,
            timestamp: new Date().toISOString()
        });
    } else {
        console.log('Mixpanel Event (Dev):', event, properties);
        mixpanel.track(event, {
            ...properties,
            environment: environment,
            timestamp: new Date().toISOString()
        });
    }
};

app.use(cors());
app.use('/assets', express.static(path.join(__dirname, 'public')));
app.use(express.json({ limit: '10mb' }));

app.get('/script.js', async (req, res) => {
    const currentDate = new Date();
    const formattedDate = `${currentDate.toLocaleDateString()} ${currentDate.toLocaleTimeString()}`;
    const clientID = req.query.cli || '';
    const tourFetchExpireAgeInSec = 300;
    const prodName = req.query.prod || 'tourComparison';
    const baseURL = environment == 'development' ? 'http://localhost:4000/api' : 'https://subanet.com/api';
    const sriptURL = environment == 'development' ? 'http://localhost:4021' : 'https://tourcomparison.subanet.com';

    // Track script request
    trackEvent('script_requested', {
        clientID,
        prodName,
        userAgent: req.headers['user-agent'],
        referer: req.headers.referer
    });

    var injectClientFn = clientID || 'clienttest' || '67b75b1b6579206a6276c3d0';
    var respFn;
    try {
        const clientScript = require(path.join(__dirname, 'injects', injectClientFn));
        respFn = clientScript.fnScript;
    } catch (error) {
        console.log('Error loading client script:', error.message);
        trackEvent('script_load_error', {
            clientID,
            error: error.message
        });
        try {
            respFn = defaultScript.fnScript;
        } catch (defaultError) {
            console.log('Error loading default script:', defaultError.message);
            trackEvent('default_script_load_error', {
                error: defaultError.message
            });
            respFn = '';
        }
    }

    var cliScript = respFn({ 
        formattedDate, 
        clientID, 
        prodName, 
        baseURL, 
        tourFetchExpireAgeInSec, 
        sriptURL,
        mixpanelToken: process.env.MIXPANEL_TOKEN // Pass token to client script
    });
    res.send(cliScript);
});

app.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}`);
    trackEvent('server_started', {
        port: PORT
    });
});
