// Dinamik olarak JavaScript içeriği olu<PERSON>
// Tatil <PERSON> inject js...

// require('dotenv').config();
// // Log environment info
// // console.log('Environment:', process.env.NODE_ENV || 'developmentX', process.env);
// const environment = process.env.NODE_ENV || 'development'
// console.log('environment:', environment);
const fnScript = ({
    formattedDate = `${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`,
    clientID = 'clienttest', tourFetchExpireAgeInSec = 300,
    prodName = 'tourComparison', baseURL = 'http://localhost:4000/api',
    sriptURL = 'http://localhost:4021',
}) => {
        const scriptContent = `
            // First define the keys to display
            const keys2Display = [
                { key: 'tourPicture', title: ' ', weight: 0.5 },
                { key: 'tourID', title: 'ID', weight: 1 },
                { key: 'tourTitle', title: 'Tur Adi', weight: 2 },
                { key: 'tourType', title: 'Tipi', weight: 3 },
                { key: 'duration', title: 'Süre (gün)', weight: 4 },
                { key: 'firstDate', title: 'Tarihler', weight: 4.1 },
                { key: 'baseStartingPrice', title: 'En Düşük Fiyat', weight: 4.5 },
                { key: 'prices', title: 'Fiyatlar', weight: 4.5 },
                { key: 'numberOfCities', title: 'Görülecek Yerler', weight: 5 },
                { key: 'cities', title: 'Sehir ve Yerler', weight: 6 },
                { key: 'startingCity', title: 'Starting City', weight: 7 },
                { key: 'transportation', title: 'Ulaşım', weight: 7.6 },
                { key: 'departurePoints', title: 'Kalkış Noktaları', weight: 7.5 },
                { key: 'includedInPackage', title: 'Dahil', weight: 20 },
                { key: 'notIncludedInPackage', title: 'Dahil Olmayanlar', weight: 25 },
                { key: 'itenary', title: 'Tur Program', weight: 30 },
                { key: 'disclaimers', title: 'Açıklamalar', weight: 31 }
            ];

            // Helper functions
            const getComparisonKeys = (data) => {
                if (!data || !Array.isArray(data)) return [];
                return keys2Display
                    .filter(k => k.weight >= 0)
                    .sort((a, b) => a.weight - b.weight)
                    .map(k => k.key)
                    .filter(key => data.some(tour => tour.hasOwnProperty(key)));
            };

            // Add createCollapsibleSection function here, before it's used
            function createCollapsibleSection(key, title, data) {
                const sectionContent = {
                    destinations: () => {
                        // Check if data exists and has the required structure
                        if (!data?.[key]?.tour1 || !data?.[key]?.tour2) {
                            console.warn('Missing data for ' + key);
                            return {
                                tour1: 'Veri bulunamadı',
                                tour2: 'Veri bulunamadı'
                            };
                        }
                        return {
                            tour1: (data[key].tour1.countries || '') + '<br>' + (data[key].tour1.cities || ''),
                            tour2: (data[key].tour2.countries || '') + '<br>' + (data[key].tour2.cities || '')
                        };
                    },
                    price_value: () => {
                        if (!data?.[key]?.tour1 || !data?.[key]?.tour2) {
                            return {
                                tour1: 'Veri bulunamadı',
                                tour2: 'Veri bulunamadı'
                            };
                        }
                        return {
                            tour1: 'Dahil:<br>' + (data[key].tour1.included || '') + '<br><br>Hariç:<br>' + (data[key].tour1.excluded || ''),
                            tour2: 'Dahil:<br>' + (data[key].tour2.included || '') + '<br><br>Hariç:<br>' + (data[key].tour2.excluded || '')
                        };
                    },
                    tour_highlights: () => {
                        if (!data?.[key]?.tour1 || !data?.[key]?.tour2) {
                            return {
                                tour1: 'Veri bulunamadı',
                                tour2: 'Veri bulunamadı'
                            };
                        }
                        return {
                            tour1: data[key].tour1.highlights || '',
                            tour2: data[key].tour2.highlights || ''
                        };
                    },
                    final_verdict: () => {
                        if (!data?.ideal_for || !data?.pros_and_cons) {
                            return {
                                tour1: 'Veri bulunamadı',
                                tour2: 'Veri bulunamadı'
                            };
                        }
                        return {
                            tour1: (data.ideal_for.tour1 || '') +
                                '<br><br>Artılar / Eksiler<br>' +
                                (data.pros_and_cons.tour1?.pros || '') + '<br><br>' +
                                (data.pros_and_cons.tour1?.cons || ''),
                            tour2: (data.ideal_for.tour2 || '') +
                                '<br><br>Artılar / Eksiler<br>' +
                                (data.pros_and_cons.tour2?.pros || '') + '<br><br>' +
                                (data.pros_and_cons.tour2?.cons || '')
                        };
                    }
                };

                try {
                    const content = sectionContent[key]();

                    // Add this variable to determine initial state
                    const isInitiallyOpen = key === 'final_verdict';

                    return '<div style="border-bottom: 1px solid #dee2e6;">' +
                        '<div class="collapsible-header" style="display: grid; grid-template-columns: 2fr 9fr 1fr; padding: 1rem; cursor: pointer; align-items: center;">' +
                            '<div style="font-weight: bold;">' + title + '</div>' +
                            '<div>' + (data[key]?.comparison || '') + '</div>' +
                            '<div class="arrow" style="text-align: right;">' + (isInitiallyOpen ? '▼' : '▶') + '</div>' +
                        '</div>' +
                        '<div class="collapsible-content" style="display: ' + (isInitiallyOpen ? 'grid' : 'none') + '; grid-template-columns: 2fr 5fr 5fr; padding: 1rem; background-color: #f8f9fa;">' +
                            '<div>&nbsp;</div>' +
                            '<div>' + content.tour1 + '</div>' +
                            '<div>' + content.tour2 + '</div>' +
                        '</div>' +
                    '</div>';
                } catch (error) {
                    console.error('Error creating section ' + key + ':', error);
                    return '<div style="border-bottom: 1px solid #dee2e6; padding: 1rem; color: red;">Veri yüklenirken hata oluştu</div>';
                }
            }

            const formatCellValue = (value) => {
                if (value === null || value === undefined) return '-';
                if (typeof value === 'boolean') return value ? 'Yes' : 'No';
                if (Array.isArray(value)) return value.join(', ');
                return value.toString();
            };

            const renderCellContent = (key, value) => {
                if (key === 'tourPicture' && value) {
                    return \`<div style="width: 100%; height: clamp(100px, 30vw, 200px); display: flex; align-items: center; justify-content: center; overflow: hidden;"><img src="\${value}" alt="Tour" style="width: 100%; height: 100%; object-fit: cover;"></div>\`;
                }

                if (key === 'datesAndPrices' && Array.isArray(value) && value.length > 0) {
                    const firstDate = value[0];
                    return \`
                        <div>
                            <div>Tour Date: \${firstDate.TourDate}</div>
                            <div>
                                \${firstDate.Prices.map(price =>
                                    \`<div>▶ \${price.FiyatTanimi} - \${price.Fiyat}</div>\`
                                ).join('')}
                            </div>
                        </div>
                    \`;
                }

                if (key === 'itenary' && Array.isArray(value)) {
                    return \`
                        <ul style="list-style-type: disc; padding-left: 20px; margin: 0;">
                            \${value.map(item => \`
                                <li style="margin-bottom: 8px;">
                                    <strong>\${item.day || ''}</strong>
                                    \${item.title ? \`<div>\${item.title}</div>\` : ''}
                                    \${item.content ? \`<div style="font-size: 0.9em; color: #666;">\${item.content}</div>\` : ''}
                                </li>
                            \`).join('')}
                        </ul>
                    \`;
                }

                if (key === 'notIncludedInPackage' && Array.isArray(value)) {
                    return \`
                        <ul style="list-style-type: disc; padding-left: 20px; margin: 0;">
                            \${value.map(item => \`
                                <li style="margin-bottom: 4px;">
                                    \${item.name || item.toString()}
                                    \${item.info ? \`<span style="color: #666; font-size: 0.9em;"> (\${item.info})</span>\` : ''}
                                </li>
                            \`).join('')}
                        </ul>
                    \`;
                }

                if (key === 'includedInPackage' && Array.isArray(value)) {
                    return \`
                        <ul style="list-style-type: disc; padding-left: 20px; margin: 0;">
                            \${value.map(item => \`<li style="margin-bottom: 4px;">\${item}</li>\`).join('')}
                        </ul>
                    \`;
                }

                if (key === 'prices' && Array.isArray(value)) {
                    return \`
                        <ul style="list-style-type: disc; padding-left: 20px; margin: 0;">
                            \${value.map(item => \`
                                <li style="margin-bottom: 4px;">
                                    \${item.title || item.toString()}
                                    \${item.tl ? \`<span style="color: #666; font-size: 0.7em;">&nbsp;&nbsp;&nbsp;(\${item.tl} TL)</span>\` : ''}
                                </li>
                            \`).join('')}

                        </ul>
                    \`;
                }


                return formatCellValue(value);
            };

            // Session ID management
            const sessionStorageKey = 'ai_compareSessionID';

            const tourFetchExpireAgeInSec = ${tourFetchExpireAgeInSec};

            function generateSessionId() {
                return 'sid_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            function getOrCreateSessionId() {
                try {
                    const saved = localStorage.getItem(sessionStorageKey);
                    if (saved) {
                        const session = JSON.parse(saved);
                        const now = new Date().getTime();
                        // Check if session has expired (24 hours)
                        if (now - session.created > 24 * 60 * 60 * 1000) {
                            // Session expired, create new one
                            const newSession = {
                                id: generateSessionId(),
                                created: now
                            };
                            localStorage.setItem(sessionStorageKey, JSON.stringify(newSession));
                            return newSession.id;
                        }
                        return session.id;
                    }
                    // No session exists, create new one
                    const newSession = {
                        id: generateSessionId(),
                        created: new Date().getTime()
                    };
                    localStorage.setItem(sessionStorageKey, JSON.stringify(newSession));
                    return newSession.id;
                } catch (error) {
                    console.warn('Session management error:', error);
                    return generateSessionId(); // Fallback to new session
                }
            }

            const sessionId = getOrCreateSessionId();
            console.log('Session ID:', sessionId);

            // Get script element and its data-key attribute
            const scriptElement = document.querySelector('script[data-key]');
            const dataKey = scriptElement ? scriptElement.getAttribute('data-key') : '';

            // Rest of the existing variables
            const pageUrl = window.location.href;

            // Çağrılan web sayfasının title bilgisini al
            const pageTitle = document.title;
            const formattedDate = '${formattedDate}';
            const clientID = '${clientID}';
            const prodName = '${prodName}';
            // Bilgileri konsola yazdır
            // console.log("Çağrılan Sayfa URL'si:", pageUrl);
            // console.log("Çağrılan Sayfanın Başlığı:", pageTitle);

            // Sayfa içeriğini değiştirme fonksiyonu
            function injectContent() {
                // Add localStorage helper functions first
                const storageKey = 'ai_compareItems';

                // Add toggleButtonStyle function at the top level of injectContent
                const toggleButtonStyle = (button, isCompare) => {
                    button.textContent = isCompare ? 'Karşılaştır' : 'Çıkart';
                    button.style.backgroundColor = isCompare ? '#007bff' : '#dc3545';
                    button.setAttribute('data-state', isCompare ? 'compare' : 'remove');
                };

                const saveToLocalStorage = (items) => {
                    try {
                        const now = new Date().getTime();
                        const expireDate = now + tourFetchExpireAgeInSec * 1000;
                        const itemsWithExpiry = items.map(item => ({ ...item, expireDate }));
                        localStorage.setItem(storageKey, JSON.stringify(itemsWithExpiry));
                    } catch (error) {
                        console.warn('Failed to save to localStorage:', error);
                    }
                };

                const loadFromLocalStorage = () => {
                    try {
                        const saved = localStorage.getItem(storageKey);
                        if (!saved) return [];

                        let items = JSON.parse(saved);
                        const now = new Date().getTime();

                        // Filter out expired items
                        items = items.filter(item => {
                            if (item.expireDate && item.expireDate <= now) {
                                // console.log('Item expired, removing:', item);
                                const storageKey = 'ai_compareItems';
                                const saved = localStorage.getItem(storageKey);
                                if(saved){
                                    let savedItems = JSON.parse(saved);
                                    savedItems = savedItems.filter(i => i.itemId !== item.itemId || i.turID !== item.turID);
                                    localStorage.setItem(storageKey, JSON.stringify(savedItems));
                                }
                                return false; // Remove expired item
                            }
                            return true; // Keep valid item
                        });

                        return items;
                    } catch (error) {
                        console.warn('Failed to load from localStorage:', error);
                        return [];
                    }
                };

                // Create or get filter panel
                // const filterPanel = document.querySelector('.filter-panel');
                const filterPanel = document.querySelector('section > div.row.sortingDiv');
                if (!filterPanel) {
                    // console.error('filter-panel öğesi bulunamadı!');
                    return;
                }

                // Create compare panel first
                let comparePanel = document.querySelector('.compare-panel');

                // Create compareAllButton
                const compareAllButton = document.createElement('button');
                compareAllButton.textContent = 'Turları Karşılaştır';
                compareAllButton.className = 'compare-all-button align-center';
                compareAllButton.style.width = 'calc(100% - 20px)';
                compareAllButton.style.margin = '0px 0px 10px 0px';
                compareAllButton.style.padding = '5px';
                compareAllButton.style.backgroundColor = '#6c757d';
                compareAllButton.style.color = '#fff';
                compareAllButton.style.border = 'none';
                compareAllButton.style.borderRadius = '5px';
                compareAllButton.style.cursor = 'not-allowed';
                compareAllButton.style.opacity = '0.65';
                compareAllButton.disabled = true;

                // Define updatePanelVisibility function before use
                const updatePanelVisibility = () => {
                    const panelBody = comparePanel?.querySelector('.panel-body');
                    const itemCount = panelBody ? panelBody.children.length : 0;
                    if (comparePanel) {
                        comparePanel.style.display = itemCount > 0 ? 'grid' : 'none';
                    }
                    if (compareAllButton) {
                        compareAllButton.disabled = itemCount < 2;
                        compareAllButton.style.backgroundColor = itemCount >= 2 ? '#28a745' : '#6c757d';
                        compareAllButton.style.cursor = itemCount >= 2 ? 'pointer' : 'not-allowed';
                        compareAllButton.style.opacity = itemCount >= 2 ? '1' : '0.65';
                    }
                };

                if (!comparePanel) {
                    comparePanel = document.createElement('div');
                    comparePanel.className = 'compare-panel panel panel-default';
                    comparePanel.style.marginTop = '0px';
                    comparePanel.style.display = 'none';
                    comparePanel.style["place-items"] = 'center';

                    // Create panel body
                    const panelBody = document.createElement('div');
                    panelBody.className = 'panel-body';
                    panelBody.style.display = 'flex';
                    panelBody.style.gap = '10px';
                    panelBody.style.wordWrap = 'break-word';
                    panelBody.style.boxSizing = 'border-box';
                    panelBody.style.width = '100%';
                    panelBody.style.justifyContent = 'center';
                    panelBody.style.padding = '0px';
                    panelBody.style.paddingTop = '10px';

                    // Create panel heading
                    const panelHeading = document.createElement('div');
                    panelHeading.className = 'panel-heading';
                    panelHeading.style.width = '100%';
                    panelHeading.innerHTML = \` Tur Karşılaştır \`;

                    comparePanel.appendChild(panelHeading);
                    comparePanel.appendChild(panelBody);
                    filterPanel.parentNode.insertBefore(comparePanel, filterPanel.nextSibling);

                    // Load saved items only if panel is newly created
                    try {
                        const savedItems = loadFromLocalStorage();
                        if (savedItems && savedItems.length > 0) {
                            const panelBody = comparePanel.querySelector('.panel-body');
                            savedItems.forEach(item => {
                                if (!item.itemId || !item.imgSrc) return;

                                const newItem = document.createElement('div');
                                newItem.setAttribute('data-item-id', item.itemId);
                                newItem.setAttribute('data-parent-url', item.parentUrl || '');
                                newItem.setAttribute('data-tur-id', item.turID || '0');
                                newItem.style.cssText = 'display: flex; flex-direction: column; margin-bottom: 10px; overflow: hidden;';

                                newItem.innerHTML = \`
                                    <div style="width: 100%; position: relative; cursor: pointer; max-width: 100%;" onclick="window.open('\${item.parentUrl}', '_blank')">
                                        <div style="width: 100%; height: clamp(100px, 30vw, 200px); display: flex; align-items: center; justify-content: center; overflow: hidden;">
                                            <img src="\${item.imgSrc}" style="width: 100%; height: 100%; object-fit: cover; max-width: 400px; min-width: 150px;" />
                                        </div>
                                        <button class="remove-btn" style="position: absolute; top: 10px; right: 10px; background-color: red; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; z-index: 2;">x</button>
                                        <span style="position: absolute; bottom: 5px; left: 5px; font-size: 10px; color: white; background-color: rgba(0, 0, 0, 0.5); padding: 2px 5px; border-radius: 3px;">\${item.title || ''}</span>
                                    </div>
                                \`;

                                const removeButton = newItem.querySelector('.remove-btn');
                                if (removeButton) {
                                    removeButton.addEventListener('click', (e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        const itemId = newItem.getAttribute('data-item-id'); // Get itemId from DOM element
                                        const turID = newItem.getAttribute('data-tur-id'); // Get turID from DOM element
                                        panelBody.removeChild(newItem);
                                        updatePanelVisibility();

                                        // Find the button in the page listing
                                        const pageButton = document.querySelector(\`button[data-item-id="\${itemId}"][data-tur-id="\${turID}"]\`);
                                        if (pageButton) {
                                            toggleButtonStyle(pageButton, true);
                                        }

                                        // Update localStorage
                                        const items = loadFromLocalStorage().filter(
                                            i => i.itemId !== itemId && i.turID !== turID
                                        );
                                        saveToLocalStorage(items);
                                    });
                                }

                                panelBody.appendChild(newItem);
                            });
                            updatePanelVisibility();
                        }
                    } catch (error) {
                        console.warn('Error loading saved items:', error);
                    }

                    comparePanel.appendChild(compareAllButton);
                    filterPanel.parentNode.insertBefore(comparePanel, filterPanel.nextSibling);
                }

                // Tüm ilgili btn-tour-list div'lerini seç
                const tourButtons = document.querySelectorAll('.btn-tour-list');

                // Her bir btn-tour-list için işlem yap
                tourButtons.forEach((tourButton) => {
                    // btn-tour-list'in parent kapsayıcısını bul
                    const container = tourButton.closest('.col-lg-4, .col-md-3, .col-sm-4, .col-xs-12');
                    if (!container) return; // Eğer kapsayıcı bulunamazsa devam etme

                    // Kapsayıcı içindeki <img> öğesini bul
                    const imgElement = container.querySelector('a > img');
                    if (!imgElement) return; // Eğer <img> öğesi yoksa devam etme

                    // Kapsayıcı içindeki <a> öğesini bul
                    const linkElement = container.querySelector('a');
                    if (!linkElement) return; // Eğer <a> öğesi yoksa devam etme

                    // Karşılaştır butonunu oluştur
                    const compareButton = document.createElement('button');
                    compareButton.textContent = 'Karşılaştır';
                    compareButton.setAttribute('data-state', 'compare');

                    // Check if this item exists in localStorage
                    const itemId = btoa(imgElement.src);
                    let turID = '0';
                    try {
                        turID = linkElement.href.split("?")[0].split('-').slice(-1)[0];
                    } catch (error) {
                        console.warn('TurID extraction failed:', error);
                    }

                    // Check if item exists in compare list
                    const savedItems = loadFromLocalStorage();
                    const isItemInList = savedItems.some(item =>
                        item.itemId === itemId && item.turID === turID
                    );

                    // Set initial button state
                    if (isItemInList) {
                        compareButton.textContent = 'Çıkart';
                        compareButton.setAttribute('data-state', 'remove');
                        compareButton.style.backgroundColor = '#dc3545';
                    } else {
                        compareButton.textContent = 'Karşılaştır';
                        compareButton.setAttribute('data-state', 'compare');
                        compareButton.style.backgroundColor = '#007bff';
                    }

                    // Rest of button styles - remove backgroundColor from here
                    compareButton.style.position = 'absolute';
                    compareButton.style.top = '10px';
                    compareButton.style.right = '10px';
                    compareButton.style.fontSize = '12px';
                    compareButton.style.padding = '5px 10px';
                    compareButton.style.color = '#fff';
                    compareButton.style.border = 'none';
                    compareButton.style.borderRadius = '5px';
                    compareButton.style.cursor = 'pointer';
                    compareButton.style.zIndex = '10';

                    // Add data-item-id to compare buttons for easier lookup
                    compareButton.setAttribute('data-item-id', itemId);
                    compareButton.setAttribute('data-tur-id', turID);

                    // Butona tıklanınca yapılacak işlem
                    compareButton.addEventListener('click', () => {
                        const isCompareState = compareButton.getAttribute('data-state') === 'compare';
                        const panelBody = comparePanel.querySelector('.panel-body');
                        const itemId = btoa(imgElement.src);

                        // Extract turID before the if block
                        let turID = '0';
                        try {
                            turID = linkElement.href.split("?")[0].split('-').slice(-1)[0];
                        } catch (error) {
                            console.warn('TurID extraction failed:', error);
                        }

                        if (isCompareState) {
                            // Check both itemId and turID for duplicates
                            const isDuplicate = Array.from(panelBody.children).some(item =>
                                item.getAttribute('data-item-id') === itemId &&
                                item.getAttribute('data-tur-id') === turID
                            );

                            if (isDuplicate) {
                                alert(\`Bu öğe zaten karşılaştırma listesinde mevcut!\nÖğe ID: \${itemId}\nTur ID: \${turID}\`);
                                return;
                            }

                            // Maksimum 2 öğe kontrolü
                            if (panelBody.children.length >= 2) {
                                alert('En fazla 2 öğe karşılaştırılabilir!');
                                return;
                            }

                            const imgTitle = imgElement.title || 'Başlık bilgisi bulunamadı';
                            const imgSrc = imgElement.src || 'Resim kaynağı bulunamadı';
                            const parentUrl = linkElement.href || 'URL bilgisi bulunamadı';

                            // Remove duplicate turID extraction since we did it above
                            const newItem = document.createElement('div');
                            newItem.setAttribute('data-item-id', itemId);
                            newItem.setAttribute('data-source-button', compareButton.id = 'btn-' + itemId);
                            newItem.setAttribute('data-parent-url', parentUrl);
                            newItem.setAttribute('data-tur-id', turID);

                            newItem.style.display = 'flex';
                            newItem.style.flex = '1';
                            newItem.style.maxWidth = '50%';
                            newItem.style.minWidth = '0';
                            newItem.style.flexDirection = 'column';
                            newItem.style.marginBottom = '10px';
                            newItem.style.overflow = 'hidden';

                            if (imgSrc && imgSrc !== 'Resim kaynağı bulunamadı') {
                                newItem.innerHTML = \`
                                    <a href="\${parentUrl}" target="_blank" style="text-decoration: none; color: inherit; display: block;">
                                        <div style="width: 100%; position: relative; max-width: 100%;">
                                            <div style="width: 100%; height: clamp(100px, 30vw, 200px); display: flex; align-items: center; justify-content: center; overflow: hidden; flex: 1;">
                                                <img src="\${imgSrc}" style="width: 100%; height: 100%; object-fit: cover; max-width: 400px; min-width: 150px;" />
                                            </div>
                                            <button class="remove-btn" style="position: absolute; top: 10px; right: 10px; background-color: red; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; z-index: 2;">x</button>
                                            <span style="position: absolute; bottom: 5px; left: 5px; font-size: 10px; color: white; background-color: rgba(0, 0, 0, 0.5); padding: 2px 5px; border-radius: 3px;">\${imgTitle}</span>
                                        </div>
                                    </a>
                                \`;
                            } else {
                                newItem.innerHTML = \`
                                    <a href="\${parentUrl}" target="_blank" style="text-decoration: none; color: inherit; display: block;">
                                        <div style="width: 100%; position: relative;">
                                            <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                                <span>Title: \${imgTitle}, Src: \${imgSrc}, URL: \${parentUrl}</span>
                                            </div>
                                            <button class="remove-btn" style="position: absolute; top: 5px; right: 5px; background-color: red; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; z-index: 2;">x</button>
                                        </div>
                                    </a>
                                \`;
                            }

                            // Add click handler to prevent remove button from triggering link
                            const removeButton = newItem.querySelector('.remove-btn');
                            if (removeButton) {
                                removeButton.addEventListener('click', (e) => {
                                    e.preventDefault(); // Prevent link click
                                    e.stopPropagation(); // Stop event bubbling
                                    panelBody.removeChild(newItem);
                                    updatePanelVisibility();
                                    toggleButtonStyle(compareButton, true);

                                    // Update localStorage
                                    const items = loadFromLocalStorage().filter(
                                        i => i.itemId !== itemId
                                    );
                                    saveToLocalStorage(items);
                                });
                            }

                            // After adding item to panel and before saving to localStorage
                            panelBody.appendChild(newItem);
                            updatePanelVisibility();
                            toggleButtonStyle(compareButton, false);

                            // Prepare item data for API
                            const itemData = {
                                itemId,
                                parentUrl,
                                turID: turID,
                                imgSrc,
                                title: imgTitle,
                                clientID,
                                prodName,
                                sessionId, // Add session ID to API calls
                            };

                            // Send POST request to API
                            fetch('${baseURL}/tours/parse', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({ item: itemData })
                            })
                            .then(response => response.json())
                            .then(data => {
                                // console.log('API Response:', data); // Veri yapısını kontrol etmek için
                            })
                            .catch(error => console.error('API Error:', error));

                            // Save updated items to localStorage
                            const items = Array.from(panelBody.children).map(item => {
                                const itemImg = item.querySelector('img');
                                const itemTitle = item.querySelector('span')?.textContent || 'Başlık bilgisi bulunamadı';
                                return {
                                    itemId: item.getAttribute('data-item-id'),
                                    parentUrl: item.getAttribute('data-parent-url'),
                                    turID: item.getAttribute('data-tur-id'),
                                    imgSrc: itemImg ? itemImg.src : '',
                                    title: itemTitle
                                };
                            });
                            saveToLocalStorage(items);
                        } else {
                            // Karşılaştırmadan çıkart
                            const item = panelBody.querySelector(\`[data-item-id="\${itemId}"]\`);
                            if (item) {
                                panelBody.removeChild(item);
                                updatePanelVisibility();
                                toggleButtonStyle(compareButton, true);

                                // Update localStorage
                                const items = loadFromLocalStorage().filter(
                                    i => i.itemId !== itemId
                                );
                                saveToLocalStorage(items);
                            }
                        }
                    });

                    // Kapsayıcıya göreli pozisyon ver (butonun absolute pozisyonlama için gerekli)
                    container.style.position = 'relative';

                    // Kapsayıcıya butonu ekle
                    container.appendChild(compareButton);
                });

                // Karşılaştırma butonuna tıklama olayını ekle
                compareAllButton.addEventListener('click', () => {
                    // Overlay oluştur
                    const overlay = document.createElement('div');
                    overlay.style.position = 'fixed';
                    overlay.style.top = '0';
                    overlay.style.left = '0';
                    overlay.style.width = '100%';
                    overlay.style.height = '100%';
                    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                    overlay.style.zIndex = '1000';

                    // Dialog oluştur
                    const dialog = document.createElement('div');
                    dialog.style.position = 'absolute';
                    dialog.style.top = '50%';
                    dialog.style.left = '50%';
                    dialog.style.transform = 'translate(-50%, -50%)';
                    dialog.style.width = '80%';
                    dialog.style.height = '90vh';
                    dialog.style.maxHeight = '90vh';
                    dialog.style.backgroundColor = '#fff';
                    dialog.style.border = '1px solid #ccc';
                    dialog.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                    dialog.style.zIndex = '1001';
                    dialog.style.overflowY = 'auto';
                    dialog.style.display = 'flex';
                    dialog.style.flexDirection = 'column';
                    dialog.style.borderRadius = '8px';

                    // Header oluştur
                    const header = document.createElement('div');
                    header.style.padding = '10px';
                    header.style.backgroundColor = '#f0f0f0';
                    header.style.borderBottom = '1px solid #ccc';
                    header.style.position = 'sticky';
                    header.style.top = '0';
                    header.style.zIndex = '1002';
                    header.style.display = 'flex';
                    header.style.justifyContent = 'space-between';
                    header.style.alignItems = 'center';

                    // Başlık ve tazele butonu için container
                    const titleContainer = document.createElement('div');
                    titleContainer.style.display = 'flex';
                    titleContainer.style.alignItems = 'center';
                    titleContainer.style.gap = '10px';

                    const title = document.createElement('span');
                    title.textContent = 'Karşılaştırma Sonuçları';
                    title.style.fontSize = '20px';
                    title.style.fontWeight = 'bold';


                    // Başlık ve tazele butonu için container
                    const BtnContainer = document.createElement('div');
                    // BtnContainer.style.display = 'flex';
                    BtnContainer.style.alignItems = 'right';
                    BtnContainer.style.gap = '10px';


                    // Tazele butonu
                    const refreshButton = document.createElement('button');
                    refreshButton.textContent = 'Tazele';
                    refreshButton.style.fontSize = '12px';
                    refreshButton.style.padding = '4px 8px';
                    // refreshButton.style.backgroundColor = '#6c757d';
                    refreshButton.style.color = '#000';
                    refreshButton.style.border = 'none';
                    refreshButton.style.borderRadius = '3px';
                    refreshButton.style.cursor = 'pointer';

                    refreshButton.addEventListener('click', () => {
                        // Cache'i temizle
                        localStorage.removeItem('ai_compareData');
                        localStorage.removeItem('ai_review');

                        // Overlay'i kaldır
                        document.body.removeChild(overlay);

                        // compareAllButton click event'ini tetikle
                        compareAllButton.click();
                    });

                    titleContainer.appendChild(title);
                    BtnContainer.appendChild(refreshButton);

                    // Tablo Goster butonu
                    const showTableButton = document.createElement('button');
                    showTableButton.textContent = 'Tablo';
                    showTableButton.style.fontSize = '12px';
                    showTableButton.style.padding = '4px 8px';
                    showTableButton.style.backgroundColor = '#a6f7e7'; // Sarı zemin
                    showTableButton.style.color = '#000';
                    showTableButton.style.border = 'none';
                    showTableButton.style.borderRadius = '3px';
                    showTableButton.style.cursor = 'pointer';
                    showTableButton.style.marginLeft = '10px';
                    showTableButton.style.marginRight = '10px';

                    // Yapay zeka icon
                    const taiIcon = document.createElement('img');
                    taiIcon.src = '${sriptURL}/assets/table.svg';
                    taiIcon.style.width = '20px';
                    taiIcon.style.height = '20px';
                    taiIcon.style.verticalAlign = 'middle';
                    taiIcon.style.marginRight = '5px';

                    showTableButton.prepend(taiIcon);

                    showTableButton.addEventListener('click', () => {
                        // Panel içeriğini bul
                        const panelBody = comparePanel.querySelector('.panel-body');
                        let tours = [];
                        if (panelBody) {
                            tours = Array.from(panelBody.children).map((item) => {
                                const itemId = item.getAttribute('data-item-id') || 'item ID bulunamadı';
                                const img = item.querySelector('img');
                                const title = item.querySelector('span')?.textContent || 'Bilgi bulunamadı';
                                const url = img ? img.src : 'URL bulunamadı';
                                const parentUrl = item.getAttribute('data-parent-url') || 'Parent URL bulunamadı';
                                const turID = item.getAttribute('data-tur-id') || '0';

                                return {
                                    turID: turID,
                                    title: title,
                                    url: parentUrl,
                                    imgSrc: url,
                                    itemID: itemId,
                                };
                            });
                        }

                        // Get selected tour IDs
                        const selectedTurIDs = tours.map(tour => tour.turID).sort();

                        // Check cache before making API call
                        const cachedData = (() => {
                            try {
                                const cached = localStorage.getItem('ai_compareData');
                                if (!cached) return null;

                                const cacheData = JSON.parse(cached);
                                const now = new Date().getTime();

                                // Check if cache has expired
                                if (now > cacheData.expireDate) {
                                    localStorage.removeItem('ai_compareData');
                                    return null;
                                }

                                // Check if cached tour IDs match current selection
                                const cachedTurIDs = cacheData.turIDs.sort();
                                const turIDsMatch = JSON.stringify(cachedTurIDs) === JSON.stringify(selectedTurIDs);

                                return turIDsMatch ? cacheData.data : null;
                            } catch (error) {
                                console.warn('Cache check failed:', error);
                                return null;
                            }
                        })();

                        if (cachedData) {
                            // Use cached data
                            // console.log('Using cached comparison data');
                            const compareData = cachedData;
                            const validKeys = getComparisonKeys(compareData);

                            let tableHTML = \`
                                <div style="overflow-x: auto; width: 100%;">
                                    <table style="width: 100%; border-collapse: collapse; min-width: 800px;">
                                        <thead>
                                            <tr>
                                                <th style="position: sticky; left: 0; background: #f8f9fa; z-index: 2; padding: 12px; border: 1px solid #dee2e6; width: 12%;">
                                                    Secilen Turlar
                                                </th>
                                                \${compareData.map((tour, index) => \`
                                                    <th style="min-width: 250px; padding: 12px; border: 1px solid #dee2e6; background: #f8f9fa; width: 44%;">
                                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                                            <span>\${tour.tourTitle || tour.title || \`Tour \${index + 1}\`}</span>
                                                            <a href="\${tour.tourUrl}" target="_blank" style="color: #0d6efd;">🔗</a>
                                                        </div>
                                                    </th>
                                                \`).join('')}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            \${validKeys.map(key => {
                                                const keyConfig = keys2Display.find(k => k.key === key);
                                                return \`
                                                    <tr>
                                                        <td style="position: sticky; left: 0; background: #fff; font-weight: bold; padding: 12px; border: 1px solid #dee2e6; vertical-align: top;">
                                                            \${keyConfig?.title || key.replace(/_/g, ' ')}
                                                        </td>
                                                        \${compareData.map(tour => \`
                                                            <td style="padding: 12px; border: 1px solid #dee2e6; vertical-align: top;">
                                                                \${renderCellContent(key, tour[key])}
                                                            </td>
                                                        \`).join('')}
                                                    </tr>
                                                \`;
                                            }).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            \`;

                            content.innerHTML = tableHTML;
                            addCollapsibleEventListeners();
                        } else {
                            // Make API request if no valid cache exists
                            fetch('${baseURL}/tours/comparetours', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    tours: tours,
                                    clientID: clientID,
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                // console.log('API Response:', data); // Veri yapısını kontrol etmek için
                                const compareData = data.data;
                                const validKeys = getComparisonKeys(compareData);

                                // Cache compare data
                                const turIDs = compareData.map(tour => tour.tourID || tour.turID).filter(id => id);
                                const cacheData = {
                                    turIDs: turIDs,
                                    expireDate: new Date().getTime() + (30 * 60 * 1000), // current time + 30 minutes
                                    data: compareData
                                };

                                try {
                                    localStorage.setItem('ai_compareData', JSON.stringify(cacheData));
                                } catch (error) {
                                    console.warn('Failed to cache compare data:', error);
                                }

                                let tableHTML = \`
                                    <div style="overflow-x: auto; width: 100%;">
                                        <table style="width: 100%; border-collapse: collapse; min-width: 800px;">
                                            <thead>
                                                <tr>
                                                    <th style="position: sticky; left: 0; background: #f8f9fa; z-index: 2; padding: 12px; border: 1px solid #dee2e6;">
                                                        Secilen Turlar
                                                    </th>
                                                    \${compareData.map((tour, index) => \`
                                                        <th style="min-width: 250px; padding: 12px; border: 1px solid #dee2e6; background: #f8f9fa;">
                                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                                <span>\${tour.tourTitle || tour.title || \`Tour \${index + 1}\`}</span>
                                                                <a href="\${tour.tourUrl}" target="_blank" style="color: #0d6efd;">🔗</a>
                                                            </div>
                                                        </th>
                                                    \`).join('')}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                \${validKeys.map(key => {
                                                    const keyConfig = keys2Display.find(k => k.key === key);
                                                    return \`
                                                        <tr>
                                                            <td style="position: sticky; left: 0; background: #fff; font-weight: bold; padding: 12px; border: 1px solid #dee2e6; vertical-align: top;">
                                                                \${keyConfig?.title || key.replace(/_/g, ' ')}
                                                            </td>
                                                            \${compareData.map(tour => \`
                                                                <td style="padding: 12px; border: 1px solid #dee2e6; vertical-align: top;">
                                                                    \${renderCellContent(key, tour[key])}
                                                                </td>
                                                            \`).join('')}
                                                        </tr>
                                                    \`;
                                                }).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                \`;

                                content.innerHTML = tableHTML;
                                addCollapsibleEventListeners();
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                content.innerHTML = '<p>Error occurred: ' + error.message + '</p>';
                            });
                        }
                        // Overlay'e dialogu ekle
                        overlay.appendChild(dialog);

                        // Body'e overlay'i ekle
                        document.body.appendChild(overlay);
                    });

                    BtnContainer.appendChild(showTableButton);

                    // Kapatma butonu
                    const closeButton = document.createElement('button');
                    closeButton.innerHTML = '&times;';
                    closeButton.style.fontSize = '24px';
                    closeButton.style.border = 'none';
                    closeButton.style.background = 'none';
                    closeButton.style.cursor = 'pointer';
                    closeButton.addEventListener('click', () => {
                        document.body.removeChild(overlay);
                    });

                    // Özetle butonu
                    const summarizeButton = document.createElement('button');
                    summarizeButton.textContent = 'Özet Tablo Göster';
                    summarizeButton.style.fontSize = '14px';
                    summarizeButton.style.padding = '8px 8px';
                    summarizeButton.style.backgroundColor = '#ffc107'; // Sarı zemin
                    summarizeButton.style.color = '#000';
                    summarizeButton.style.border = 'none';
                    summarizeButton.style.borderRadius = '3px';
                    summarizeButton.style.cursor = 'pointer';
                    summarizeButton.style.marginRight = '5px';

                    // Yapay zeka icon
                    const aiIcon = document.createElement('img');
                    aiIcon.src = '${sriptURL}/assets/ai.svg';
                    aiIcon.style.width = '20px';
                    aiIcon.style.height = '20px';
                    aiIcon.style.verticalAlign = 'middle';
                    aiIcon.style.marginRight = '5px';

                    summarizeButton.prepend(aiIcon);

                    summarizeButton.addEventListener('click', () => {
                        // Disable the button and show loading state
                        summarizeButton.disabled = true;
                        summarizeButton.style.opacity = '0.65';
                        summarizeButton.style.cursor = 'not-allowed';

                        // Create loading indicator
                        const loadingHTML = \`
                            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 40px;">
                                <div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite;"></div>
                                <div style="margin-top: 20px; font-size: 16px; color: #666;">Yapay zeka özet hazırlıyor...</div>
                            </div>
                            <style>
                                @keyframes spin {
                                    0% { transform: rotate(0deg); }
                                    100% { transform: rotate(360deg); }
                                }
                            </style>
                        \`;

                        content.innerHTML = loadingHTML;

                        // Extract comparedata (tours array)
                        const panelBody = comparePanel.querySelector('.panel-body');
                        let tours = [];
                        if (panelBody) {
                            tours = Array.from(panelBody.children).map((item) => {
                                const itemId = item.getAttribute('data-item-id') || 'item ID bulunamadı';
                                const img = item.querySelector('img');
                                const title = item.querySelector('span')?.textContent || 'Bilgi bulunamadı';
                                const url = img ? img.src : 'URL bulunamadı';
                                const parentUrl = item.getAttribute('data-parent-url') || 'Parent URL bulunamadı';
                                const turID = item.getAttribute('data-tur-id') || '0';

                                return {
                                    turID: turID,
                                    title: title,
                                    url: parentUrl,
                                    imgSrc: url,
                                    itemID: itemId,
                                };
                            });
                        }

                        // Get selected tour IDs
                        const selectedTurIDs = tours.map(tour => tour.turID).sort();

                        // Check cache first
                        try {
                            const cached = localStorage.getItem('ai_review');
                            if (cached) {
                                const reviewData = JSON.parse(cached);
                                const now = new Date().getTime();
                                const cachedTurIDs = reviewData.turIDs.sort();
                                const currentTurIDs = tours.map(tour => tour.turID).sort();

                                // Check if cache is valid and matches current selection
                                if (now < reviewData.expireDate &&
                                    JSON.stringify(cachedTurIDs) === JSON.stringify(currentTurIDs)) {
                                    console.log('Using cached AI review');
                                    const parsedDataStg = typeof reviewData.data === 'string' ?
                                        JSON.parse(reviewData.data) : reviewData.data;

                                    const parsedData = parsedDataStg ? parsedDataStg?.data.result : {}

                                    // console.log('parsedData localcache!', parsedData);
                                    // Create and set table HTML using parsedData
                                    const tableHTML = \`
                                        <div style="width: 100%; max-width: 1200px; margin: 0 auto;">
                                                <div style="border-bottom: 1px solid #dee2e6; padding-bottom: 3px; margin-bottom: 1rem;">
                                                    Karşılaştırma Özeti
                                                </div>
                                            <!-- Basic Info Section -->
                                            <div style="border-bottom: 1px solid #dee2e6; padding-bottom: 1rem;">
                                                \${parsedData.basic_info?.comparison || ''}
                                            </div>

                                            <!-- Tour Titles Header -->
                                            <div style="display: grid; grid-template-columns: 2fr 5fr 5fr; background-color: #f8f9fa; padding: 1rem; font-weight: bold;">
                                                <div>Özellik</div>
                                                <div>\${parsedData.basic_info?.tour1.tour_title || ''}</div>
                                                <div>\${parsedData.basic_info?.tour2.tour_title || ''}</div>
                                            </div>

                                            <!-- Basic Info Details -->
                                            <div style="display: grid; grid-template-columns: 2fr 5fr 5fr; padding: 1rem; border-bottom: 1px solid #dee2e6;">
                                                <div style="font-weight: bold;">Temel Bilgiler</div>
                                                <div>
                                                    <div>\${parsedData.basic_info?.tour1.duration || ''} (\${parsedData.basic_info?.tour1.tour_startdate || ''})</div>
                                                    <div>\${parsedData.basic_info?.tour1.price_range || ''}</div>
                                                    <div>\${parsedData.basic_info?.tour1.transportation || ''}</div>
                                                </div>
                                                <div>
                                                    <div>\${parsedData.basic_info?.tour2.duration || ''} (\${parsedData.basic_info?.tour2.tour_startdate || ''})</div>
                                                    <div>\${parsedData.basic_info?.tour2.price_range || ''}</div>
                                                    <div>\${parsedData.basic_info?.tour2.transportation || ''}</div>
                                                </div>
                                            </div>

                                            <!-- Collapsible Sections -->
                                            \${createCollapsibleSection('destinations', 'Destinasyonlar', parsedData)}
                                            \${createCollapsibleSection('price_value', 'Fiyat ve Kapsam', parsedData)}
                                            \${createCollapsibleSection('tour_highlights', 'Öne Çıkanlar', parsedData)}
                                            \${createCollapsibleSection('final_verdict', 'Genel Değerlendirme', parsedData)}
                                        </div>
                                    \`;
                                    content.innerHTML = tableHTML;
                                    addCollapsibleEventListeners();
                                    // Re-enable the button after loading from cache
                                    summarizeButton.disabled = false;
                                    summarizeButton.style.opacity = '1';
                                    summarizeButton.style.cursor = 'pointer';
                                    return;
                                } else {
                                    localStorage.removeItem('ai_review');
                                }
                            }
                        } catch (error) {
                            console.warn('Cache check failed:', error);
                            localStorage.removeItem('ai_review');
                        }

                        // Make API request if no valid cache exists
                        fetch('${baseURL}/tours/reviewtourssimulation', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                comparedata: tours,
                                clientID: clientID,
                                sessionId: sessionId,
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            // Cache the response
                            const aiReviewData = {
                                turIDs: tours.map(tour => tour.turID),
                                expireDate: new Date().getTime() + (30 * 60 * 1000),
                                data: data
                            };
                            localStorage.setItem('ai_review', JSON.stringify(aiReviewData));

                            // Parse the data if it's a string, otherwise use as is
                            const parsedDataStg = typeof data === 'string' ? JSON.parse(data) : data;

                            const parsedData = parsedDataStg ? parsedDataStg?.data.result : {}
                            // console.log('parsedData server!', parsedData);

                            // Create the table structure
                            const tableHTML = \`
                                <div style="width: 100%; max-width: 1200px; margin: 0 auto;">
                                    <div style="border-bottom: 1px solid #dee2e6; padding-bottom: 3px; margin-bottom: 1rem;">
                                        Karşılaştırma Özeti
                                    </div>

                                    <!-- Basic Info Section -->
                                    <div style="border-bottom: 1px solid #dee2e6; padding-bottom: 1rem;">
                                        \${parsedData.basic_info?.comparison || ''}
                                    </div>

                                    <!-- Tour Titles Header -->
                                    <div style="display: grid; grid-template-columns: 2fr 5fr 5fr; background-color: #f8f9fa; padding: 1rem; font-weight: bold;">
                                        <div>Özellik</div>
                                        <div>\${parsedData.basic_info?.tour1.tour_title || ''}</div>
                                        <div>\${parsedData.basic_info?.tour2.tour_title || ''}</div>
                                    </div>

                                    <!-- Basic Info Details -->
                                    <div style="display: grid; grid-template-columns: 2fr 5fr 5fr; padding: 1rem; border-bottom: 1px solid #dee2e6;">
                                        <div style="font-weight: bold;">Temel Bilgiler</div>
                                        <div>
                                            <div>\${parsedData.basic_info?.tour1.duration || ''} (\${parsedData.basic_info?.tour1.tour_startdate || ''})</div>
                                            <div>\${parsedData.basic_info?.tour1.price_range || ''}</div>
                                            <div>\${parsedData.basic_info?.tour1.transportation || ''}</div>
                                        </div>
                                        <div>
                                            <div>\${parsedData.basic_info?.tour2.duration || ''} (\${parsedData.basic_info?.tour2.tour_startdate || ''})</div>
                                            <div>\${parsedData.basic_info?.tour2.price_range || ''}</div>
                                            <div>\${parsedData.basic_info?.tour2.transportation || ''}</div>
                                        </div>
                                    </div>

                                    <!-- Collapsible Sections -->
                                    \${createCollapsibleSection('destinations', 'Destinasyonlar', parsedData)}
                                    \${createCollapsibleSection('price_value', 'Fiyat ve Kapsam', parsedData)}
                                    \${createCollapsibleSection('tour_highlights', 'Öne Çıkanlar', parsedData)}
                                    \${createCollapsibleSection('final_verdict', 'Genel Değerlendirme', parsedData)}
                                </div>
                            \`;

                            content.innerHTML = tableHTML;
                            addCollapsibleEventListeners();

                            // Re-enable the button after successful load
                            summarizeButton.disabled = false;
                            summarizeButton.style.opacity = '1';
                            summarizeButton.style.cursor = 'pointer';
                        })
                        .catch(error => {
                            console.error('AI review error:', error);
                            content.innerHTML = '<p>Error occurred while processing the data: ' + error.message + '</p>';

                            // Re-enable the button after error
                            summarizeButton.disabled = false;
                            summarizeButton.style.opacity = '1';
                            summarizeButton.style.cursor = 'pointer';
                        });
                    });

                    header.appendChild(titleContainer);

                    BtnContainer.appendChild(summarizeButton);
                    BtnContainer.appendChild(closeButton);

                    // header.appendChild(summarizeButton);
                    // header.appendChild(closeButton);
                    header.appendChild(BtnContainer);
                    dialog.appendChild(header);

                    // İçerik alanı
                    const content = document.createElement('div');
                    content.style.padding = '20px';
                    dialog.appendChild(content);

                    // Yükleniyor mesajı
                    content.innerHTML = '<p>Yükleniyor...</p>';

                    // Panel içeriğini bul
                    const panelBody = comparePanel.querySelector('.panel-body');
                    let tours = [];
                    if (panelBody) {
                        tours = Array.from(panelBody.children).map((item) => {
                            const itemId = item.getAttribute('data-item-id') || 'item ID bulunamadı';
                            const img = item.querySelector('img');
                            const title = item.querySelector('span')?.textContent || 'Bilgi bulunamadı';
                            const url = img ? img.src : 'URL bulunamadı';
                            const parentUrl = item.getAttribute('data-parent-url') || 'Parent URL bulunamadı';
                            const turID = item.getAttribute('data-tur-id') || '0';

                            return {
                                turID: turID,
                                title: title,
                                url: parentUrl,
                                imgSrc: url,
                                itemID: itemId,
                            };
                        });
                    }

                    // Get selected tour IDs
                    const selectedTurIDs = tours.map(tour => tour.turID).sort();

                    // Check cache before making API call
                    const cachedData = (() => {
                        try {
                            const cached = localStorage.getItem('ai_compareData');
                            if (!cached) return null;

                            const cacheData = JSON.parse(cached);
                            const now = new Date().getTime();

                            // Check if cache has expired
                            if (now > cacheData.expireDate) {
                                localStorage.removeItem('ai_compareData');
                                return null;
                            }

                            // Check if cached tour IDs match current selection
                            const cachedTurIDs = cacheData.turIDs.sort();
                            const turIDsMatch = JSON.stringify(cachedTurIDs) === JSON.stringify(selectedTurIDs);

                            return turIDsMatch ? cacheData.data : null;
                        } catch (error) {
                            console.warn('Cache check failed:', error);
                            return null;
                        }
                    })();

                    if (cachedData) {
                        // Use cached data
                        // console.log('Using cached comparison data');
                        const compareData = cachedData;
                        const validKeys = getComparisonKeys(compareData);

                        let tableHTML = \`
                            <div style="overflow-x: auto; width: 100%;">
                                <table style="width: 100%; border-collapse: collapse; min-width: 800px;">
                                    <thead>
                                        <tr>
                                            <th style="position: sticky; left: 0; background: #f8f9fa; z-index: 2; padding: 12px; border: 1px solid #dee2e6; width: 12%;">
                                                Secilen Turlar
                                            </th>
                                            \${compareData.map((tour, index) => \`
                                                <th style="min-width: 250px; padding: 12px; border: 1px solid #dee2e6; background: #f8f9fa; width: 44%;">
                                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                                        <span>\${tour.tourTitle || tour.title || \`Tour \${index + 1}\`}</span>
                                                        <a href="\${tour.tourUrl}" target="_blank" style="color: #0d6efd;">🔗</a>
                                                    </div>
                                                </th>
                                            \`).join('')}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        \${validKeys.map(key => {
                                            const keyConfig = keys2Display.find(k => k.key === key);
                                            return \`
                                                <tr>
                                                    <td style="position: sticky; left: 0; background: #fff; font-weight: bold; padding: 12px; border: 1px solid #dee2e6; vertical-align: top;">
                                                        \${keyConfig?.title || key.replace(/_/g, ' ')}
                                                    </td>
                                                    \${compareData.map(tour => \`
                                                        <td style="padding: 12px; border: 1px solid #dee2e6; vertical-align: top;">
                                                            \${renderCellContent(key, tour[key])}
                                                        </td>
                                                    \`).join('')}
                                                </tr>
                                            \`;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        \`;

                        content.innerHTML = tableHTML;
                        addCollapsibleEventListeners();
                    } else {
                        // Make API request if no valid cache exists
                        fetch('${baseURL}/tours/comparetours', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                tours: tours,
                                clientID: clientID,
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            // console.log('API Response:', data); // Veri yapısını kontrol etmek için
                            const compareData = data.data;
                            const validKeys = getComparisonKeys(compareData);

                            // Cache compare data
                            const turIDs = compareData.map(tour => tour.tourID || tour.turID).filter(id => id);
                            const cacheData = {
                                turIDs: turIDs,
                                expireDate: new Date().getTime() + (30 * 60 * 1000), // current time + 30 minutes
                                data: compareData
                            };

                            try {
                                localStorage.setItem('ai_compareData', JSON.stringify(cacheData));
                            } catch (error) {
                                console.warn('Failed to cache compare data:', error);
                            }

                            let tableHTML = \`
                                <div style="overflow-x: auto; width: 100%;">
                                    <table style="width: 100%; border-collapse: collapse; min-width: 800px;">
                                        <thead>
                                            <tr>
                                                <th style="position: sticky; left: 0; background: #f8f9fa; z-index: 2; padding: 12px; border: 1px solid #dee2e6;">
                                                    Secilen Turlar
                                                </th>
                                                \${compareData.map((tour, index) => \`
                                                    <th style="min-width: 250px; padding: 12px; border: 1px solid #dee2e6; background: #f8f9fa;">
                                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                                            <span>\${tour.tourTitle || tour.title || \`Tour \${index + 1}\`}</span>
                                                            <a href="\${tour.tourUrl}" target="_blank" style="color: #0d6efd;">🔗</a>
                                                        </div>
                                                    </th>
                                                \`).join('')}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            \${validKeys.map(key => {
                                                const keyConfig = keys2Display.find(k => k.key === key);
                                                return \`
                                                    <tr>
                                                        <td style="position: sticky; left: 0; background: #fff; font-weight: bold; padding: 12px; border: 1px solid #dee2e6; vertical-align: top;">
                                                            \${keyConfig?.title || key.replace(/_/g, ' ')}
                                                        </td>
                                                        \${compareData.map(tour => \`
                                                            <td style="padding: 12px; border: 1px solid #dee2e6; vertical-align: top;">
                                                                \${renderCellContent(key, tour[key])}
                                                            </td>
                                                        \`).join('')}
                                                    </tr>
                                                \`;
                                            }).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            \`;

                            content.innerHTML = tableHTML;
                            addCollapsibleEventListeners();
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            content.innerHTML = '<p>Error occurred: ' + error.message + '</p>';
                        });
                    }

                    // Overlay'e dialogu ekle
                    overlay.appendChild(dialog);

                    // Body'e overlay'i ekle
                    document.body.appendChild(overlay);
                });

                // compare-panel'in altına karşılaştırma butonunu ekle
                comparePanel.appendChild(compareAllButton);
            }
            injectContent();

            // Add this function inside the script content
            const addCollapsibleEventListeners = () => {
                document.querySelectorAll('.collapsible-header').forEach(header => {
                    header.addEventListener('click', function() {
                        const content = this.nextElementSibling;
                        const arrow = this.querySelector('.arrow');
                        if (content.style.display === 'none' || !content.style.display) {
                            content.style.display = 'grid';
                            arrow.textContent = '▼';
                        } else {
                            content.style.display = 'none';
                            arrow.textContent = '▶';
                        }
                    });
                });
            };
        `;

    return scriptContent
};
module.exports = {fnScript}
