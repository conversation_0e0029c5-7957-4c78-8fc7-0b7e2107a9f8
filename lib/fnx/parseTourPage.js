import axios from 'axios';
import * as cheerio from 'cheerio';
const fs = require('fs').promises;
const path = require('path');

export async function parseTourPage(url, clientSchema, fetchConfig = {}) {
    try {
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
                ...fetchConfig.headers,
            },
            ...fetchConfig,
        });
        const $ = cheerio.load(response.data);
        let tourData = {}
        try {
            // const fileName = new URL(url).pathname.split('/').pop() || 'index';
            const fileName = 'parsed';
            const filePath = path.join(process.cwd(), '/lib/repo/parsed', `${fileName}.html`);
            console.log('path.dirname(filePath)', path.dirname(filePath))
            await fs.mkdir(path.dirname(filePath), { recursive: true });
            await fs.writeFile(filePath, response.data);
        } catch (error) {
            console.error('Error saving HTML file:', error.message);
        }

        // Import customer-specific parsing functions
        let parseFunctions;
        if (clientSchema) {
            try {
                // Using relative import path
                parseFunctions = await import(`./parseFunctions/${clientSchema}.js`);
                console.log('parseFunctions loaded:', clientSchema);
                tourData = {
                    ...tourData,
                    greet: parseFunctions.greet(),
                    //...await customerFunctions.default($)
                };
            } catch (error) {
                console.error(`Error importing customer functions for ${clientSchema}:`, error);
            }
        }

        // Extract tour details
        const tourName = parseFunctions.extractTourName($);
        const itenary = parseFunctions?.parseItenary($);
        const includedServices = parseFunctions?.extractIncludedServices($)
        const notIncludedServices = parseFunctions?.extractNotIncludedServices($);
        const tourDates = parseFunctions?.extractTourDates($);
        const priceTables = parseFunctions?.extractPriceTables($);
        const minPrice = parseFunctions?.extractPriceTablesMinPrices($);
        const cities = parseFunctions?.extractVisitedPlaces($);
        const otherinfo = parseFunctions?.extractTourFeatures($);
        const tourDescription = parseFunctions?.extractTourDescription($);
        const departurePoints = parseFunctions?.extractDeparturePoints($);
        const tourType = parseFunctions?.extractBreadcrumbToJson($);
        const transportation = parseFunctions?.extractTransportationInfo($);

        tourData = { ...tourData,
            tourName: tourName, //$('.tour-detail h1').text().trim(),
            description: tourDescription,
            itenary,
            tourType,
            transportation,
            includedServices,
            notIncludedServices,
            dates: tourDates,
            prices: priceTables,
            minPrice: minPrice,
            cities,
            departurePoints,
            ...otherinfo,
            // Add more selectors as needed based on the page structure
            parsedAt: new Date(),
        };
        
        return tourData;
    } catch (error) {
        console.error('Error parsing tour page:', url, clientSchema, error);
        throw error;
    }
}