export function greet() {
    return "<PERSON>r<PERSON><PERSON>, Tatil Sepeti!";
}
export function parseItenaryx(html) {
    const doc = html;
    
    // Tur programını tutacak sonuç dizisi
    const tourProgram = [];

    // Gün başlıklarını seçmek için regex
    const dayHeaderRegex = /(\d+)\.\s*Gün\s*:\s*(.*)/;

    // HTML içindeki tüm <div> elemanlarını bul
    const contentDivs = doc.querySelectorAll('.facility-features-right div');

    // Geçerli günü takip etmek için değişkenler
    let currentDay = null;
    let currentTitle = null;
    let currentActivities = [];

    // Her bir div'i işle
    contentDivs.forEach(div => {
        const text = div.textContent.trim();

        // Eğer bir gün başlığı varsa (örneğin "1. Gün : İstanbul – Roma")
        const dayMatch = text.match(dayHeaderRegex);
        if (dayMatch) {
            // Önceki günü tamamla
            if (currentDay !== null) {
                tourProgram.push({
                    day: currentDay,
                    title: currentTitle,
                    content: currentActivities.join("\n\n") // Aktiviteleri birleştirerek tek bir string yap
                });
            }

            // Yeni günü başlat
            currentDay = parseInt(dayMatch[1], 10);
            currentTitle = dayMatch[2].trim();
            currentActivities = [];
        } else if (currentDay !== null && text.length > 0) {
            // Eğer bir gün başlığı yoksa ve mevcut gün varsa, aktiviteleri ekle
            currentActivities.push(text);
        }
    });

    // Son günü de ekleyelim
    if (currentDay !== null) {
        tourProgram.push({
            day: currentDay,
            title: currentTitle,
            content: currentActivities.join("\n\n") // Aktiviteleri birleştirerek tek bir string yap
        });
    }

    return tourProgram;
}

export function parseItenary(html) {
    const $ = html;
    const program = [];
    // Find the panel with "Tur Programı" heading
    $('div.panel').each(function () {
        const heading = $(this).find('.panel-heading h2').text().trim();
        if (heading === 'Tur Programı') {
            // Loop through each day in the program
            $(this).find('.panel-body .media').each(function () {
                const day = $(this).find('.media-left .media-object').text().trim();
                const title = $(this).find('.media-heading').text().trim();
                const content = $(this).find('.media-content').text().trim();

                // Extract meals and accommodation info from content
                const details = content.split('<br>').filter(Boolean);
                const mainContent = details[0];
                const extras = details.slice(1).map(d => d.trim()).filter(Boolean);

                let meals = {};
                let accommodation = '';

                extras.forEach(extra => {
                    if (extra.startsWith('Kahvaltı:')) {
                        meals.breakfast = extra.replace('Kahvaltı:', '').trim();
                    } else if (extra.startsWith('Öğle Yemeği:')) {
                        meals.lunch = extra.replace('Öğle Yemeği:', '').trim();
                    } else if (extra.startsWith('Akşam Yemeği:')) {
                        meals.dinner = extra.replace('Akşam Yemeği:', '').trim();
                    } else if (extra.startsWith('Konaklama:')) {
                        accommodation = extra.replace('Konaklama:', '').trim();
                    }
                });


                program.push({
                    day: day,
                    title: title,
                    content: mainContent,
                    // meals: meals,
                    // accommodation: accommodation
                });
            });
        }
    });

    return program;
} 

export function extractNotIncludedServices(html) {
    const $ = html;
    const notIncludedServices = [];

    // Find the panel with "Fiyata Dahil Olmayan Hizmetler" heading
    $('div.panel').each(function () {
        const heading = $(this).find('.panel-heading h3').text().trim();
        if (heading === 'Fiyata Dahil Olmayan Hizmetler') {
            // Loop through each list item in the panel body
            $(this).find('.panel-body ol.list-group li').each(function () {
                const service = {
                    name: $(this).find('.col-lg-7').text().replace(/^\s*•\s*|\s*<i.*?>\s*|\s+$/g, '').trim(),
                    info: $(this).find('[data-original-title]').attr('data-original-title') || ''
                };

                if (service.name) {
                    notIncludedServices.push(service);
                }
            });
        }
    });

    return notIncludedServices;
};
export function extractIncludedServices(html) {
    const $ = html;
    const includedServices = [];

    // Find the panel with "Fiyata Dahil Hizmetler" heading
    $('div.panel').each(function () {
        const heading = $(this).find('.panel-heading h2').text().trim();
        if (heading === 'Fiyata Dahil Hizmetler') {
            // Loop through each list item in the panel body
            $(this).find('.panel-body ol.list-group li').each(function () {
                const service = $(this).text().replace(/^\s*•\s*|\s*<i.*?>\s*|\s+$/g, '').trim();
                if (service) {
                    includedServices.push(service);
                }
            });
        }
    });

    return includedServices;
}
export function extractTourDates($) {
    const dates = [];
    // Tur Tarihleri dropdown'ını bul
    $('#tourChangeDate option').each(function () {
        const date = {
            startDate: $(this).attr('data-startdate'),
            endDate: $(this).attr('data-enddate'),
            value: $(this).attr('val'),
            text: $(this).text().trim(),
            // isSelected: $(this).is(':selected')
        };
        dates.push(date);
    });

    return dates;
};
export function extractVisitedPlaces($) {
    const places = [];
    // tour-detail__features sınıfına sahip div içindeki ul altındaki li elementlerini bul
    $('.tour-detail__features ul li').each(function () {
        const place = $(this).text().trim();
        if (place) {
            places.push(place);
        }
    });

    return places;
};
export function extractTourFeatures($) {
    const features = {};
    // tour-detail__features__info altındaki her satırı bul
    $('.tour-detail__features__info').each(function () {
        $(this).find('.row').each(function () {
            const label = $(this).find('.col-lg-4').text().trim();
            const value = $(this).find('.col-lg-8').text().trim();

            // Boş olmayan label ve value'ları ekle
            if (label && value) {
                // Label'dan ":" karakterini temizle
                const cleanLabel = label.replace(':', '').trim();
                features[cleanLabel] = value;
            }
        });
    });
    return features;
};
export function extractDeparturePoints($) {
    const departurePoints = [];
    // "Tur Kalkış Noktaları" başlıklı paneli bul
    $('div.panel').each(function() {
        const heading = $(this).find('.panel-heading h3').text().trim();
        if (heading === 'Tur Kalkış Noktaları') {

            // Panel body içindeki list-group altındaki her bir maddeyi bul
            $(this).find('.panel-body .list-group li').each(function() {
                const point = $(this).text().replace(/^\s*[^A-Za-zğüşöçıİĞÜŞÖÇ\d]*\s*/, '').trim();
                if (point) {
                    departurePoints.push(point);
                }
            });
        }
    });
    return departurePoints;
};
export function extractTourDescription($) {
    const descriptions = [];
    // "Açıklamalar" başlıklı paneli bul
    $('div.panel').each(function() {
        const heading = $(this).find('.panel-heading h3').text().trim();
        if (heading === 'Açıklamalar') {
            // #descriptionTour div'i içindeki metni al
            const text = $(this).find('#descriptionTour').html().trim();
            if (text) {
                descriptions.push(text);
            }
        }
    });
    return descriptions;
};

function extractCurrencyInfo(inputText) {
    // Regex: Sayılar ve para birimlerini eşle
    const regex = /([\d.,]+)\s*(EUR|USD|TL)/g;

    // Sonuçları saklamak için bir dizi oluştur
    const results = [];
    let match;
    while ((match = regex.exec(inputText)) !== null) {
        // Eşleşen değeri ayrıştır
        const amount = parseFloat(match[1].replace(/\./g, '').replace(',', '.')); // Virgülü noktaya çevir ve sayıyı dönüştür
        const currency = match[2].toLowerCase(); // Para birimini küçük harfe çevir (EUR -> eur, USD -> usd, TL -> tl)
        const originalText = match[0]; // Orijinal metin

        // Sonuçları diziye ekle
        results.push({
            amount: amount,
            currency: currency,
            originalText: originalText
        });
    }

    return results;
}

export function extractPriceTables($) {
    const tables = [];
    // tourPrice div'i altındaki her price-table class'ına sahip div için
    $('#tourPrice .price-table').each(function () {
        const table = {
            title: '',
            prices: {
                doubleRoom: {},
                extraBed: {},
                singleRoom: {},
                child: {
                    ages: [],
                    prices: []
                }
            }
        };

        // Başlık
        const titleDiv = $(this).find('.price-table__title__name .middle').first();
        if (titleDiv.length) {
            table.title = titleDiv.text().trim();
        }

        // Çift Kişilik Oda Fiyatı 
        const doubleRoomPrice = $(this).find('.price-table__body__item .col-lg-4').eq(0).find('.discount-price');
        if (doubleRoomPrice.length) {
            let exchI = extractCurrencyInfo(doubleRoomPrice.text());
            Array.isArray(exchI) && exchI.map(e => {
                let exc = e.currency;
                table.prices.doubleRoom[exc] = e.amount
            })
        }

        // İlave Yatak Fiyatı
        const extraBedPrice = $(this).find('.price-table__body__item .col-lg-4').eq(1).find('.discount-price');
        if (extraBedPrice.length) {
            let exchI = extractCurrencyInfo(extraBedPrice.text());
            Array.isArray(exchI) && exchI.map(e => {
                let exc = e.currency;
                table.prices.extraBed[exc] = e.amount
            })
        }

        // Tek Kişilik Oda Fiyatı
        const singleRoomPrice = $(this).find('.price-table__body__item .col-lg-4').eq(2).find('.discount-price');
        if (singleRoomPrice.length) {
            let exchI = extractCurrencyInfo(singleRoomPrice.text());
            Array.isArray(exchI) && exchI.map(e => {
                let exc = e.currency;
                table.prices.singleRoom[exc] = e.amount
            })
        }

        // Çocuk Yaş Aralıkları ve Fiyatları
        $(this).find('.price-table__header__name--small').each(function () {
            table.prices.child.ages.push($(this).text().trim());
        });

        $(this).find('.child-div .price-table__body__cell .discount-price').each(function () {
            table.prices.child.prices.push($(this).text().trim());
            // let exchI = extractCurrencyInfo($(this).text());
            // Array.isArray(exchI) && exchI.map(e => {
            //     let exc = e.currency;
            //     table.prices.child.prices[exc] = e.amount
            // })
        });

        tables.push(table);
    });

    return tables;
};

export function extractPriceTables_v1($) {
    const tables = [];
    // tourPrice div'i altındaki her price-table class'ına sahip div için
    $('#tourPrice .price-table').each(function () {
        const table = {
            title: '',
            prices: {
                doubleRoom: {
                    eur: '',
                    tl: ''
                },
                extraBed: {
                    eur: '',
                    tl: ''
                },
                singleRoom: {
                    eur: '',
                    tl: ''
                },
                child: {
                    ages: [],
                    prices: []
                }
            }
        };

        // Başlık
        const titleDiv = $(this).find('.price-table__title__name .middle').first();
        if (titleDiv.length) {
            table.title = titleDiv.text().trim();
        }

        // Çift Kişilik Oda Fiyatı 
        const doubleRoomPrice = $(this).find('.price-table__body__item .col-lg-4').eq(0).find('.discount-price');
        if (doubleRoomPrice.length) {
            let exchR = 'tl';
            let exchR2 = 'tl';
            try {
                const exch = doubleRoomPrice.first().text().trim().split(' ')[1].toLowerCase();
                exchR = exch ? exch : exchR;
                // console.log('exch', doubleRoomPrice.first().text().trim(), exch, exchR);
            }
            catch (eR) { }
            table.prices.doubleRoom[exchR] = doubleRoomPrice.first().text().trim();
            if (doubleRoomPrice.length > 1) {
                try {
                    const exch2 = doubleRoomPrice.last().text().trim().split(' ')[1].toLowerCase();
                    exchR2 = exch2 ? exch2 : exchR2;
                    // console.log('exch', doubleRoomPrice.last().text().trim(), exchR2, exchR2);
                }
                catch (eR) { }

                table.prices.doubleRoom.tl = doubleRoomPrice.last().text().trim();
            }
        }

        // İlave Yatak Fiyatı
        const extraBedPrice = $(this).find('.price-table__body__item .col-lg-4').eq(1).find('.discount-price');
        if (extraBedPrice.length) {
            table.prices.extraBed.eur = extraBedPrice.first().text().trim();
            if (extraBedPrice.length > 1) {
                table.prices.extraBed.tl = extraBedPrice.last().text().trim();
            }
        }

        // Tek Kişilik Oda Fiyatı
        const singleRoomPrice = $(this).find('.price-table__body__item .col-lg-4').eq(2).find('.discount-price');
        if (singleRoomPrice.length) {
            table.prices.singleRoom.eur = singleRoomPrice.first().text().trim();
            if (singleRoomPrice.length > 1) {
                table.prices.singleRoom.tl = singleRoomPrice.last().text().trim();
            }
        }

        // Çocuk Yaş Aralıkları ve Fiyatları
        $(this).find('.price-table__header__name--small').each(function () {
            table.prices.child.ages.push($(this).text().trim());
        });

        $(this).find('.child-div .price-table__body__cell .discount-price').each(function () {
            table.prices.child.prices.push($(this).text().trim());
        });

        tables.push(table);
    });

    return tables;
};


export function extractPriceTablesMinPrices($) {
    const tables = [];
    // tourPrice div'i altındaki her price-table class'ına sahip div için
    $('#tourPrice .price-table').each(function () {
        const table = {
            title: '',
            prices: {
                doubleRoom: {},
                extraBed: {},
                singleRoom: {},
                child: {
                    ages: [],
                    prices: []
                }
            }
        };

        // Başlık
        const titleDiv = $(this).find('.price-table__title__name .middle').first();
        if (titleDiv.length) {
            table.title = titleDiv.text().trim();
        }

        // Çift Kişilik Oda Fiyatı 
        const doubleRoomPrice = $(this).find('.price-table__body__item .col-lg-4').eq(0).find('.discount-price');
        if (doubleRoomPrice.length) {
            let exchI = extractCurrencyInfo(doubleRoomPrice.text());
            Array.isArray(exchI) && exchI.map(e => {
                let exc = e.currency;
                table.prices.doubleRoom[exc] = e.amount
            })
        }

        // İlave Yatak Fiyatı
        const extraBedPrice = $(this).find('.price-table__body__item .col-lg-4').eq(1).find('.discount-price');
        if (extraBedPrice.length) {
            let exchI = extractCurrencyInfo(extraBedPrice.text());
            Array.isArray(exchI) && exchI.map(e => {
                let exc = e.currency;
                table.prices.extraBed[exc] = e.amount
            })
        }

        // Tek Kişilik Oda Fiyatı
        const singleRoomPrice = $(this).find('.price-table__body__item .col-lg-4').eq(2).find('.discount-price');
        if (singleRoomPrice.length) {
            let exchI = extractCurrencyInfo(singleRoomPrice.text());
            Array.isArray(exchI) && exchI.map(e => {
                let exc = e.currency;
                table.prices.singleRoom[exc] = e.amount
            })
        }

        // Çocuk Yaş Aralıkları ve Fiyatları
        $(this).find('.price-table__header__name--small').each(function () {
            table.prices.child.ages.push($(this).text().trim());
        });

        $(this).find('.child-div .price-table__body__cell .discount-price').each(function () {
            table.prices.child.prices.push($(this).text().trim());
            // let exchI = extractCurrencyInfo($(this).text());
            // Array.isArray(exchI) && exchI.map(e => {
            //     let exc = e.currency;
            //     table.prices.child.prices[exc] = e.amount
            // })
        });

        tables.push(table);
    });

    function getMinPrice(data) {
        // prices nesnesini döngüye alarak minimum TL değerini bulalım
        const prices = data.prices;
        let minFiyat = { odaTipi: "", eur: null, tl: 1000000 }; //Infinity
    
        for (const [odaTipi, fiyatlar] of Object.entries(prices)) {
            // child kategorisini hariç tut
            if (odaTipi === "child") continue;
    
            console.log('odaTipi, fiyatlar', odaTipi, fiyatlar);
            // Diğer oda tipleri için TL değerini kontrol edelim
            const tlStr = fiyatlar.tl;
            if (tlStr) {
                const tl = parseFloat(tlStr.replace(".", "").replace(",", ".").replace(" TL", ""));
                if (tl < minFiyat.tl) {
                    minFiyat = {
                        odaTipi,
                        eur: parseFloat(fiyatlar.eur.replace(",", ".").replace(" EUR", "")),
                        tl
                    };
                }
            }
        }
    
        // Sonuç nesnesini oluştur
        return {
            title: data.title,
            minFiyat
        };
    }
    function findMinTlPrice(data) {
    
        // Oda tiplerini dinamik olarak belirleyen yardımcı fonksiyon
        function getRoomTypesToCheck(prices) {
            const excludedTypes = ["child", "extraBed"]; // Görmezden gelinmesi gereken oda tipleri
            return Object.keys(prices).filter(key => !excludedTypes.includes(key));
        }
    
        let minPrice = Infinity; // Başlangıçta sonsuz değer atıyoruz
        let roomType = ""; // En düşük fiyatı veren oda tipi
    
        const pTitle = data.title;
        const prices = data.prices;
    
        // Dinamik olarak oda tiplerini belirle
        const roomTypesToCheck = getRoomTypesToCheck(prices);

        // Belirlenen oda tiplerini kontrol etme
        roomTypesToCheck.forEach(roomTypeToCheck => {
            if (prices[roomTypeToCheck] && prices[roomTypeToCheck].tl) {
                const roomPriceTl = parseFloat(prices[roomTypeToCheck].tl);
                if (!isNaN(roomPriceTl) && roomPriceTl < minPrice) {
                    minPrice = roomPriceTl;
                    roomType = roomTypeToCheck;
                }
            }
        });
    
        // Eğer hiçbir fiyat bulunamazsa varsayılan değer döndür
        if (!isFinite(minPrice)) {
            return {
                title: '',
                odaTipi: "",
                tl: null
            };
        }
    
        // En düşük fiyatı ve oda tipini döndür
        return {
            title: pTitle,
            odaTipi: roomType,
            tl: minPrice
        };
    }
    const priceMins = [];
    Array.isArray(tables) && tables.map(p => {
        // console.log('pTable', p)
        let minPrice = findMinTlPrice(p);
        priceMins.push(minPrice);
    })
    return priceMins;
};
export function extractTourName($) {
    return $('.tour-detail h1').text().trim();
}

export function extractBreadcrumbToJson($) {
    // Breadcrumb verilerini çıkarmak için bir dizi oluşturuyoruz
    const breadcrumbArray = [];
    // Her bir <li> elemanını seçip işliyoruz
    $(".breadcrumb__main li").each((index, element) => {
        const link = $(element).find("a");
        const url = link.attr("href"); // href özelliğini alıyoruz
        const title = link.attr("title"); // title özelliğini alıyoruz
        const text = link.find("span").text(); // <span> içeriğini alıyoruz

        // Verileri bir obje olarak dizide saklıyoruz
        breadcrumbArray.push({
            url: url,
            title: title,
            text: text
        });
    });

    return breadcrumbArray;
}
export function extractTransportationInfo($) {
    let transportationInfo = null;
    // "tour-detail__features__info" sınıfını seç
    const infoHtml = $(".tour-detail__features__info").html();
    if (infoHtml) {
        // Regex ile "Ulaşım:" sonrasındaki metni bul
        const match = infoHtml.match(/<strong>Ulaşım:<\/strong>\s*([^<]+)/);
        if (match) {
            transportationInfo = match[1].trim(); // Metni temizle
        }
    }

    return transportationInfo;

    // "tour-detail__features__info" sınıfını seç ve içindeki <strong> etiketlerini kontrol et
    // $(".tour-detail__features__info strong").each((index, element) => {
    //     console.log('transportation', element)
    //     const strongText = $(element).text().trim(); // <strong> içeriğini al
    //     console.log('strongText', strongText)
    //     if (strongText === "Ulaşım:") {
    //         // "Ulaşım:" bulunduysa, aynı satırdaki metni al
    //         console.log('strongText sub', strongText, $(element).next().text(), 'nnext ended')
    //         transportationInfo = $(element).next().text().trim() || 
    //                              $(element).parent().contents().filter(function () {
    //                                  return this.nodeType === 3; // Metin düğümlerini filtrele
    //                              }).eq(0).text().trim();
    //         console.log('transportationInfo ', transportationInfo)
    //     }
    // });
    return transportationInfo;
}
