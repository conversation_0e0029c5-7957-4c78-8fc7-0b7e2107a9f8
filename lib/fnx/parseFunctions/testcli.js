export function greet() {
    return "<PERSON><PERSON><PERSON><PERSON>, Tatil Sepeti!";
}
export function extractTourName($) {
    return $('section.hotel-title > h1').text().trim();
}
export function extractNotIncludedServices(html) {
    const $ = html;
    const includedServicesDiv = $('#tour-terms > div:nth-child(1) > div.col-md-18.spanfix > div > div.row > div:nth-child(2)');
    const lines = includedServicesDiv.children()
        .map((index, element) => {
            const text = $(element).text().trim();
            // const resp = text == 'Ücrete Dahil Olan <PERSON>zmetler' ? '' : text;
            return text; // Boşlukları temizle ve metni döndür
        })
        .get() // Cheerio nesnesini düz bir diziye dönüştür
        .filter(text => text.length > 0 && text !== 'Ücrete Dahil <PERSON>'); // Boş satırları filtrele
    return lines;
};
export function extractIncludedServices(html) {
    const $ = html;
    const includedServicesDiv = $('#tour-terms > div:nth-child(1) > div.col-md-18.spanfix > div > div.row > div:nth-child(1)');
    const lines = includedServicesDiv.children()
        .map((index, element) => {
            const text = $(element).text().trim();
            // const resp = text == 'Ücrete Dahil Olan Hizmetler' ? '' : text;
            return text; // Boşlukları temizle ve metni döndür
        })
        .get() // Cheerio nesnesini düz bir diziye dönüştür
        .filter(text => text.length > 0 && text !== 'Ücrete Dahil Olan Hizmetler'); // Boş satırları filtrele
    return lines;
}
export function parseItenary(html) {
    const $ = html;
    const itenary = [];
    const includedServicesDiv = $('#tour-program > div > div.col-md-18 > div.facility-features-right > div');
    const lines = includedServicesDiv.children()
        .map((index, element) => {
            const hasCildren = $(element).children().length > 0;
            const text = $(element).text().trim();
            if (!hasCildren) {
                text.length > 0 && itenary.push(text);
            } else {
                // Çocuk div'lerini işleyelim
                $(element).children().each(function () {
                    const childText = $(this).text().trim();
                    if (childText.length > 0) {
                        itenary.push(childText);
                    }
                });
            }
        });
    return itenary;
}
export function extractTourDescription(html) {
    const descriptions = [];
    // "Açıklamalar" başlıklı paneli bul #visa-information > div > div.col-md-18 > div > div
    const $ = html;
    const includedServicesDiv = $('#visa-information > div > div.col-md-18 > div > div');
    const lines = includedServicesDiv.children()
        .map((index, element) => {
            const text = $(element).text().trim();
            return text; // Boşlukları temizle ve metni döndür
        })
        .get() // Cheerio nesnesini düz bir diziye dönüştür
        .filter(text => text.length > 0); // Boş satırları filtrele
    return lines;
};
export function extractVisitedPlaces(html) {
    // tour-detail__features sınıfına sahip div içindeki ul altındaki li elementlerini bul
    try {
        const $ = html;
        const places = $('#tab1 > div > div.col-md-17 > div > div > ul > li:nth-child(4) > span').text().trim();
        return places;
    } catch (e) {
        console.error('Error extracting visited places:', e);
        return null;
    }
};
export function extractTourFeatures($) {
    return null;
};
export function extractDeparturePoints($) {
    return null;
};
export function extractTourDates($) {
    try {
        // Tur Tarihleri dropdown'ını bul
        const dateElements = $('.period-text-sub');
        const dates = [];
        
        dateElements.each((index, element) => {
            const dateRange = $(element).text().trim();
            if (dateRange) {
                const startDate = dateRange.split(' - ')[0].trim();
                dates.push(startDate);
            }
        });
        
        return dates.length > 0 ? dates[0] : null;
    } catch (e) {
        console.error('Error extracting TourDates:', e);
        return null;
    }
};

function extractCurrencyInfo(inputText) {
    // Regex: Sayılar ve para birimlerini eşle
    const regex = /([\d.,]+)\s*(EUR|USD|TL)/g;

    // Sonuçları saklamak için bir dizi oluştur
    const results = [];
    let match;
    while ((match = regex.exec(inputText)) !== null) {
        // Eşleşen değeri ayrıştır
        const amount = parseFloat(match[1].replace(/\./g, '').replace(',', '.')); // Virgülü noktaya çevir ve sayıyı dönüştür
        const currency = match[2].toLowerCase(); // Para birimini küçük harfe çevir (EUR -> eur, USD -> usd, TL -> tl)
        const originalText = match[0]; // Orijinal metin

        // Sonuçları diziye ekle
        results.push({
            amount: amount,
            currency: currency,
            originalText: originalText
        });
    }

    return results;
}

export function extractPriceTables($) {
    const tables = [];
    // tourPrice div'i altındaki her price-table class'ına sahip div için
    $('#tourPrice .price-table').each(function () {
        const table = {
            title: '',
            prices: {
                doubleRoom: {},
                extraBed: {},
                singleRoom: {},
                child: {
                    ages: [],
                    prices: []
                }
            }
        };

        // Başlık
        const titleDiv = $(this).find('.price-table__title__name .middle').first();
        if (titleDiv.length) {
            table.title = titleDiv.text().trim();
        }

        // Çift Kişilik Oda Fiyatı 
        const doubleRoomPrice = $(this).find('.price-table__body__item .col-lg-4').eq(0).find('.discount-price');
        if (doubleRoomPrice.length) {
            let exchI = extractCurrencyInfo(doubleRoomPrice.text());
            Array.isArray(exchI) && exchI.map(e => {
                let exc = e.currency;
                table.prices.doubleRoom[exc] = e.amount
            })
        }

        // İlave Yatak Fiyatı
        const extraBedPrice = $(this).find('.price-table__body__item .col-lg-4').eq(1).find('.discount-price');
        if (extraBedPrice.length) {
            let exchI = extractCurrencyInfo(extraBedPrice.text());
            Array.isArray(exchI) && exchI.map(e => {
                let exc = e.currency;
                table.prices.extraBed[exc] = e.amount
            })
        }

        // Tek Kişilik Oda Fiyatı
        const singleRoomPrice = $(this).find('.price-table__body__item .col-lg-4').eq(2).find('.discount-price');
        if (singleRoomPrice.length) {
            let exchI = extractCurrencyInfo(singleRoomPrice.text());
            Array.isArray(exchI) && exchI.map(e => {
                let exc = e.currency;
                table.prices.singleRoom[exc] = e.amount
            })
        }

        // Çocuk Yaş Aralıkları ve Fiyatları
        $(this).find('.price-table__header__name--small').each(function () {
            table.prices.child.ages.push($(this).text().trim());
        });

        $(this).find('.child-div .price-table__body__cell .discount-price').each(function () {
            table.prices.child.prices.push($(this).text().trim());
            // let exchI = extractCurrencyInfo($(this).text());
            // Array.isArray(exchI) && exchI.map(e => {
            //     let exc = e.currency;
            //     table.prices.child.prices[exc] = e.amount
            // })
        });

        tables.push(table);
    });

    return tables;
};
export function extractPriceTablesMinPrices($) {
    // Tarih bilgisini çek
    const date = $('span.period-text-sub').text().trim();

    // Kişi sayısı bilgisini çek
    const personCount = $('div:contains("Kişi Sayısı") span').text().trim();

    // Fiyat bilgisini çek
    const priceElement = $('div:contains("Tarih")').next().first();
    const price = priceElement.text().trim(); // Fiyatı alıyoruz

    console.log('price', price, priceElement.html());

    // Sonuç objesini oluştur
    const result = {
        date,
        personCount,
        price
    };

    return result;
};
export function extractBreadcrumbToJson($) {
    // Breadcrumb verilerini çıkarmak için bir dizi oluşturuyoruz
    const breadcrumbArray = [];
    // Her bir <li> elemanını seçip işliyoruz
    $(".breadcrumb__main li").each((index, element) => {
        const link = $(element).find("a");
        const url = link.attr("href"); // href özelliğini alıyoruz
        const title = link.attr("title"); // title özelliğini alıyoruz
        const text = link.find("span").text(); // <span> içeriğini alıyoruz

        // Verileri bir obje olarak dizide saklıyoruz
        breadcrumbArray.push({
            url: url,
            title: title,
            text: text
        });
    });

    return breadcrumbArray;
}
export function extractTransportationInfo($) {
    let transportationInfo = null;
    // "tour-detail__features__info" sınıfını seç
    const infoHtml = $(".tour-detail__features__info").html();
    if (infoHtml) {
        // Regex ile "Ulaşım:" sonrasındaki metni bul
        const match = infoHtml.match(/<strong>Ulaşım:<\/strong>\s*([^<]+)/);
        if (match) {
            transportationInfo = match[1].trim(); // Metni temizle
        }
    }

    return transportationInfo;
}
