// server.js
const express = require('express');
const cors = require('cors');
const fs = require('fs'); // File System modülü
const path = require('path'); // Dosya yollarını yönetmek için
const app = express();
const PORT = 8001;
app.use(cors());
// app.use(express.json());
app.use(express.json({ limit: '10mb' }));
app.post('/api/parsepage', (req, res) => {
    const { url, title, content } = req.body; // Gelen veriyi çek

    // Gelen veriyi logla
    console.log('Gelen Veri:');
    console.log('URL:', url);
    console.log('Başlık:', title);

    // Kaydedilecek dosya adını oluştur (örneğin, "page-<timestamp>.html")
    const timestamp = Date.now(); // Benzersiz bir zaman damgası
    const fileName = `page-${timestamp}.html`;
    const filePath = path.join(__dirname, 'pages', fileName); // "pages" klasörüne kaydet

    // HTML içeriğini bir dosyaya yaz
    fs.writeFile(filePath, content, (err) => {
        if (err) {
            console.error('Dosya Yazma Hatası:', err);
            return res.status(500).json({
                status: 'error',
                message: 'HTML içeriği kaydedilirken bir hata oluştu.'
            });
        }

        console.log(`HTML içeriği "${fileName}" dosyasına başarıyla kaydedildi.`);

        // Başarı yanıtını döndür
        res.json({
            status: 'success',
            message: 'Sayfa içeriği başarıyla kaydedildi.',
            // savedFile: fileName
        });
    });
});

// Statik dosyaları sunma

// Statik dosyaları sunma
app.get('/script.js', (req, res) => {
    // script.js dosyasını gönder
    res.setHeader('Content-Type', 'application/javascript');
    // Yanıtın JavaScript olduğu belirtiliyor
    res.setHeader('Content-Type', 'application/javascript');

    // Günün tarihini ve saatini al
    const currentDate = new Date();
    const formattedDate = `${currentDate.toLocaleDateString()} ${currentDate.toLocaleTimeString()}`;
    // Get clientID from query string if it exists
    const clientID = req.query.cli || '';
    const prodName = req.query.prod || 'tourComparison';
    const parseURL = 'http://localhost:3000/api/script/parse';
    // Dinamik olarak JavaScript içeriği oluştur
    const scriptContent = `
        // Session ID management
        const sessionStorageKey = 'ai_compareSessionID';
        
        function generateSessionId() {
            return 'sid_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function getOrCreateSessionId() {
            try {
                const saved = localStorage.getItem(sessionStorageKey);
                if (saved) {
                    const session = JSON.parse(saved);
                    const now = new Date().getTime();
                    // Check if session has expired (24 hours)
                    if (now - session.created > 24 * 60 * 60 * 1000) {
                        // Session expired, create new one
                        const newSession = {
                            id: generateSessionId(),
                            created: now
                        };
                        localStorage.setItem(sessionStorageKey, JSON.stringify(newSession));
                        return newSession.id;
                    }
                    return session.id;
                }
                // No session exists, create new one
                const newSession = {
                    id: generateSessionId(),
                    created: new Date().getTime()
                };
                localStorage.setItem(sessionStorageKey, JSON.stringify(newSession));
                return newSession.id;
            } catch (error) {
                console.warn('Session management error:', error);
                return generateSessionId(); // Fallback to new session
            }
        }

        const sessionId = getOrCreateSessionId();
        console.log('Session ID:', sessionId);

        // Get script element and its data-key attribute
        const scriptElement = document.querySelector('script[data-key]');
        const dataKey = scriptElement ? scriptElement.getAttribute('data-key') : '';
        
        // Rest of the existing variables
        const pageUrl = window.location.href;

        // Çağrılan web sayfasının title bilgisini al
        const pageTitle = document.title;
        const formattedDate = '${formattedDate}';
        const clientID = '${clientID}';
        const prodName = '${prodName}';
        // Bilgileri konsola yazdır
        console.log("Çağrılan Sayfa URL'si:", pageUrl);
        console.log("Çağrılan Sayfanın Başlığı:", pageTitle);

        // Sayfa içeriğini değiştirme fonksiyonu
        function injectContent() {
            // Add localStorage helper functions first
            const storageKey = 'ai_compareItems';
            
            // Add toggleButtonStyle function at the top level of injectContent
            const toggleButtonStyle = (button, isCompare) => {
                button.textContent = isCompare ? 'Karşılaştır' : 'Çıkart';
                button.style.backgroundColor = isCompare ? '#007bff' : '#dc3545';
                button.setAttribute('data-state', isCompare ? 'compare' : 'remove');
            };

            const saveToLocalStorage = (items) => {
                try {
                    localStorage.setItem(storageKey, JSON.stringify(items));
                } catch (error) {
                    console.warn('Failed to save to localStorage:', error);
                }
            };

            const loadFromLocalStorage = () => {
                try {
                    const saved = localStorage.getItem(storageKey);
                    return saved ? JSON.parse(saved) : [];
                } catch (error) {
                    console.warn('Failed to load from localStorage:', error);
                    return [];
                }
            };

            // Create or get filter panel
            const filterPanel = document.querySelector('.filter-panel');
            if (!filterPanel) {
                console.error('filter-panel öğesi bulunamadı!');
                return;
            }

            // Create compare panel first
            let comparePanel = document.querySelector('.compare-panel');
            
            // Create compareAllButton
            const compareAllButton = document.createElement('button');
            compareAllButton.textContent = 'Tümünü Karşılaştır';
            compareAllButton.className = 'compare-all-button';
            compareAllButton.style.width = '100%';
            compareAllButton.style.marginTop = '10px';
            compareAllButton.style.padding = '10px';
            compareAllButton.style.backgroundColor = '#6c757d';
            compareAllButton.style.color = '#fff';
            compareAllButton.style.border = 'none';
            compareAllButton.style.borderRadius = '5px';
            compareAllButton.style.cursor = 'not-allowed';
            compareAllButton.style.opacity = '0.65';
            compareAllButton.disabled = true;

            // Define updatePanelVisibility function before use
            const updatePanelVisibility = () => {
                const panelBody = comparePanel?.querySelector('.panel-body');
                const itemCount = panelBody ? panelBody.children.length : 0;
                if (comparePanel) {
                    comparePanel.style.display = itemCount > 0 ? 'block' : 'none';
                }
                if (compareAllButton) {
                    compareAllButton.disabled = itemCount < 2;
                    compareAllButton.style.backgroundColor = itemCount >= 2 ? '#28a745' : '#6c757d';
                    compareAllButton.style.cursor = itemCount >= 2 ? 'pointer' : 'not-allowed';
                    compareAllButton.style.opacity = itemCount >= 2 ? '1' : '0.65';
                }
            };

            if (!comparePanel) {
                comparePanel = document.createElement('div');
                comparePanel.className = 'compare-panel panel panel-default';
                comparePanel.style.marginTop = '5px';
                comparePanel.style.display = 'none';

                // Create panel body
                const panelBody = document.createElement('div');
                panelBody.className = 'panel-body';
                panelBody.style.maxWidth = '100%';
                panelBody.style.wordWrap = 'break-word';

                // Create panel heading
                const panelHeading = document.createElement('div');
                panelHeading.className = 'panel-heading';
                panelHeading.innerHTML = \`
                    Karşılaştır
                    <a href="#" class="panel-heading__clear hidden-xs hidden-sm" style="float: right; margin-left: 10px;">Temizle</a>
                    <a href="#" class="panel-heading__close" style="float: right;">×</a>
                \`;

                comparePanel.appendChild(panelHeading);
                comparePanel.appendChild(panelBody);
                filterPanel.parentNode.insertBefore(comparePanel, filterPanel.nextSibling);

                // Load saved items only if panel is newly created
                try {
                    const savedItems = loadFromLocalStorage();
                    if (savedItems && savedItems.length > 0) {
                        const panelBody = comparePanel.querySelector('.panel-body');
                        savedItems.forEach(item => {
                            if (!item.itemId || !item.imgSrc) return;

                            const newItem = document.createElement('div');
                            newItem.setAttribute('data-item-id', item.itemId);
                            newItem.setAttribute('data-parent-url', item.parentUrl || '');
                            newItem.setAttribute('data-tur-id', item.turId || '0');
                            newItem.style.cssText = 'display: flex; flex-direction: column; margin-bottom: 10px; overflow: hidden;';
                            
                            newItem.innerHTML = \`
                                <div style="width: 100%; position: relative; cursor: pointer;" onclick="window.open('\${item.parentUrl}', '_blank')">
                                    <img src="\${item.imgSrc}" style="width: 100%; height: auto; object-fit: contain;" />
                                    <button class="remove-btn" style="position: absolute; top: 10px; right: 10px; background-color: red; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; z-index: 2;">x</button>
                                    <span style="position: absolute; bottom: 5px; left: 5px; font-size: 10px; color: white; background-color: rgba(0, 0, 0, 0.5); padding: 2px 5px; border-radius: 3px;">\${item.title || ''}</span>
                                </div>
                            \`;

                            const removeButton = newItem.querySelector('.remove-btn');
                            if (removeButton) {
                                removeButton.addEventListener('click', (e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    const itemId = newItem.getAttribute('data-item-id'); // Get itemId from DOM element
                                    const turId = newItem.getAttribute('data-tur-id'); // Get turId from DOM element
                                    panelBody.removeChild(newItem);
                                    updatePanelVisibility();

                                    // Find the button in the page listing
                                    const pageButton = document.querySelector(\`button[data-item-id="\${itemId}"][data-tur-id="\${turId}"]\`);
                                    if (pageButton) {
                                        toggleButtonStyle(pageButton, true);
                                    }

                                    // Update localStorage
                                    const items = loadFromLocalStorage().filter(
                                        i => i.itemId !== itemId && i.turId !== turId
                                    );
                                    saveToLocalStorage(items);
                                });
                            }

                            panelBody.appendChild(newItem);
                        });
                        updatePanelVisibility();
                    }
                } catch (error) {
                    console.warn('Error loading saved items:', error);
                }

                comparePanel.appendChild(compareAllButton);
                filterPanel.parentNode.insertBefore(comparePanel, filterPanel.nextSibling);
            }

            // Tüm ilgili btn-tour-list div'lerini seç
            const tourButtons = document.querySelectorAll('.btn-tour-list');

            // Her bir btn-tour-list için işlem yap
            tourButtons.forEach((tourButton) => {
                // btn-tour-list'in parent kapsayıcısını bul
                const container = tourButton.closest('.col-lg-4, .col-md-3, .col-sm-4, .col-xs-12');
                if (!container) return; // Eğer kapsayıcı bulunamazsa devam etme

                // Kapsayıcı içindeki <img> öğesini bul
                const imgElement = container.querySelector('a > img');
                if (!imgElement) return; // Eğer <img> öğesi yoksa devam etme

                // Kapsayıcı içindeki <a> öğesini bul
                const linkElement = container.querySelector('a');
                if (!linkElement) return; // Eğer <a> öğesi yoksa devam etme

                // Karşılaştır butonunu oluştur
                const compareButton = document.createElement('button');
                compareButton.textContent = 'Karşılaştır';
                compareButton.setAttribute('data-state', 'compare');
                
                // Check if this item exists in localStorage
                const itemId = btoa(imgElement.src);
                let turID = '0';
                try {
                    turID = linkElement.href.split("?")[0].split('-').slice(-1)[0];
                } catch (error) {
                    console.warn('TurID extraction failed:', error);
                }

                // Check if item exists in compare list
                const savedItems = loadFromLocalStorage();
                const isItemInList = savedItems.some(item => 
                    item.itemId === itemId && item.turId === turID
                );

                // Set initial button state
                if (isItemInList) {
                    compareButton.textContent = 'Çıkart';
                    compareButton.setAttribute('data-state', 'remove');
                    compareButton.style.backgroundColor = '#dc3545';
                } else {
                    compareButton.textContent = 'Karşılaştır';
                    compareButton.setAttribute('data-state', 'compare');
                    compareButton.style.backgroundColor = '#007bff';
                }

                // Rest of button styles - remove backgroundColor from here
                compareButton.style.position = 'absolute';
                compareButton.style.top = '10px';
                compareButton.style.right = '10px';
                compareButton.style.fontSize = '12px';
                compareButton.style.padding = '5px 10px';
                compareButton.style.color = '#fff';
                compareButton.style.border = 'none';
                compareButton.style.borderRadius = '5px';
                compareButton.style.cursor = 'pointer';
                compareButton.style.zIndex = '10';

                // Add data-item-id to compare buttons for easier lookup
                compareButton.setAttribute('data-item-id', itemId);
                compareButton.setAttribute('data-tur-id', turID);

                // Butona tıklanınca yapılacak işlem
                compareButton.addEventListener('click', () => {
                    const isCompareState = compareButton.getAttribute('data-state') === 'compare';
                    const panelBody = comparePanel.querySelector('.panel-body');
                    const itemId = btoa(imgElement.src);
                    
                    // Extract turID before the if block
                    let turID = '0';
                    try {
                        turID = linkElement.href.split("?")[0].split('-').slice(-1)[0];
                    } catch (error) {
                        console.warn('TurID extraction failed:', error);
                    }

                    if (isCompareState) {
                        // Check both itemId and turID for duplicates
                        const isDuplicate = Array.from(panelBody.children).some(item => 
                            item.getAttribute('data-item-id') === itemId &&
                            item.getAttribute('data-tur-id') === turID
                        );

                        if (isDuplicate) {
                            alert(\`Bu öğe zaten karşılaştırma listesinde mevcut!\nÖğe ID: \${itemId}\nTur ID: \${turID}\`);
                            return;
                        }

                        // Maksimum 2 öğe kontrolü
                        if (panelBody.children.length >= 2) {
                            alert('En fazla 2 öğe karşılaştırılabilir!');
                            return;
                        }

                        const imgTitle = imgElement.title || 'Başlık bilgisi bulunamadı';
                        const imgSrc = imgElement.src || 'Resim kaynağı bulunamadı';
                        const parentUrl = linkElement.href || 'URL bilgisi bulunamadı';

                        // Remove duplicate turID extraction since we did it above
                        const newItem = document.createElement('div');
                        newItem.setAttribute('data-item-id', itemId);
                        newItem.setAttribute('data-source-button', compareButton.id = 'btn-' + itemId);
                        newItem.setAttribute('data-parent-url', parentUrl);
                        newItem.setAttribute('data-tur-id', turID);

                        newItem.style.display = 'flex'; // Change to flex
                        newItem.style.flexDirection = 'column'; // Stack children vertically
                        newItem.style.marginBottom = '10px';
                        newItem.style.overflow = 'hidden';

                        if (imgSrc && imgSrc !== 'Resim kaynağı bulunamadı') {
                            newItem.innerHTML = \`
                                <a href="\${parentUrl}" target="_blank" style="text-decoration: none; color: inherit; display: block;">
                                    <div style="width: 100%; position: relative;">
                                        <img src="\${imgSrc}" style="width: 100%; height: auto; object-fit: contain;" />
                                        <button class="remove-btn" style="position: absolute; top: 10px; right: 10px; background-color: red; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; z-index: 2;">x</button>
                                        <span style="position: absolute; bottom: 5px; left: 5px; font-size: 10px; color: white; background-color: rgba(0, 0, 0, 0.5); padding: 2px 5px; border-radius: 3px;">\${imgTitle}</span>
                                    </div>
                                </a>
                            \`;
                        } else {
                            newItem.innerHTML = \`
                                <a href="\${parentUrl}" target="_blank" style="text-decoration: none; color: inherit; display: block;">
                                    <div style="width: 100%; position: relative;">
                                        <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                            <span>Title: \${imgTitle}, Src: \${imgSrc}, URL: \${parentUrl}</span>
                                        </div>
                                        <button class="remove-btn" style="position: absolute; top: 5px; right: 5px; background-color: red; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; z-index: 2;">x</button>
                                    </div>
                                </a>
                            \`;
                        }

                        // Add click handler to prevent remove button from triggering link
                        const removeButton = newItem.querySelector('.remove-btn');
                        if (removeButton) {
                            removeButton.addEventListener('click', (e) => {
                                e.preventDefault(); // Prevent link click
                                e.stopPropagation(); // Stop event bubbling
                                panelBody.removeChild(newItem);
                                updatePanelVisibility();
                                toggleButtonStyle(compareButton, true);

                                // Update localStorage
                                const items = loadFromLocalStorage().filter(
                                    i => i.itemId !== itemId
                                );
                                saveToLocalStorage(items);
                            });
                        }

                        // After adding item to panel and before saving to localStorage
                        panelBody.appendChild(newItem);
                        updatePanelVisibility();
                        toggleButtonStyle(compareButton, false);

                        // Prepare item data for API
                        const itemData = {
                            itemId,
                            parentUrl,
                            turId: turID,
                            imgSrc,
                            title: imgTitle,
                            clientID,
                            prodName,
                            sessionId, // Add session ID to API calls
                        };

                        // Send POST request to API
                        fetch('${parseURL}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ item: itemData })
                        })
                        .then(response => response.json())
                        .then(data => console.log('API Response:', data))
                        .catch(error => console.error('API Error:', error));

                        // Save updated items to localStorage
                        const items = Array.from(panelBody.children).map(item => {
                            const itemImg = item.querySelector('img');
                            const itemTitle = item.querySelector('span')?.textContent || 'Başlık bilgisi bulunamadı';
                            return {
                                itemId: item.getAttribute('data-item-id'),
                                parentUrl: item.getAttribute('data-parent-url'),
                                turId: item.getAttribute('data-tur-id'),
                                imgSrc: itemImg ? itemImg.src : '',
                                title: itemTitle
                            };
                        });
                        saveToLocalStorage(items);
                    } else {
                        // Karşılaştırmadan çıkart
                        const item = panelBody.querySelector(\`[data-item-id="\${itemId}"]\`);
                        if (item) {
                            panelBody.removeChild(item);
                            updatePanelVisibility();
                            toggleButtonStyle(compareButton, true);

                            // Update localStorage
                            const items = loadFromLocalStorage().filter(
                                i => i.itemId !== itemId
                            );
                            saveToLocalStorage(items);
                        }
                    }
                });

                // Kapsayıcıya göreli pozisyon ver (butonun absolute pozisyonlama için gerekli)
                container.style.position = 'relative';

                // Kapsayıcıya butonu ekle
                container.appendChild(compareButton);
            });

            // Karşılaştırma butonuna tıklama olayını ekle
            compareAllButton.addEventListener('click', () => {
                // Diyalog kutusunu oluştur
                const dialog = document.createElement('div');
                dialog.style.position = 'fixed';
                dialog.style.top = '50%';
                dialog.style.left = '50%';
                dialog.style.transform = 'translate(-50%, -50%)';
                dialog.style.width = '50%';
                dialog.style.height = 'auto';
                dialog.style.backgroundColor = '#fff';
                dialog.style.border = '1px solid #ccc';
                dialog.style.padding = '20px';
                dialog.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                dialog.style.zIndex = '1000';
                dialog.style.textAlign = 'center';

                // Yükleniyor metni
                dialog.innerHTML = \`<p>Yükleniyor...</p>\`;

                // Karşılaştırma bilgilerini ekleyeceğimiz alan
                const comparisonList = document.createElement('ul');
                comparisonList.style.listStyleType = 'none';
                comparisonList.style.padding = '0';
                comparisonList.style.marginTop = '10px';

                // Panel içeriğini bul
                const panelBody = comparePanel.querySelector('.panel-body');
                if (panelBody) {
                    // Paneldeki tüm öğeleri döngüye sok
                    Array.from(panelBody.children).forEach((item) => {
                        const img = item.querySelector('img');
                        const title = item.querySelector('span')?.textContent || 'Bilgi bulunamadı';
                        const url = img ? img.src : 'URL bulunamadı';
                        const parentUrl = item.getAttribute('data-parent-url') || 'Parent URL bulunamadı';
                        const turID = item.getAttribute('data-tur-id') || '0';

                        // Liste öğesi oluştur
                        const listItem = document.createElement('li');
                        listItem.style.marginBottom = '10px';
                        listItem.innerHTML = \`
                            <div>
                                <strong>Başlık:</strong> \${title}<br>
                                <strong>URL:</strong> <a href="\${parentUrl}" target="_blank">\${parentUrl}</a><br>
                                <strong>Tur ID:</strong> \${turID}
                            </div>
                        \`;
                        comparisonList.appendChild(listItem);
                    });
                }

                // Yükleniyor metnini kaldır ve karşılaştırma bilgilerini göster
                setTimeout(() => {
                    dialog.innerHTML = '';
                    dialog.appendChild(comparisonList);
                }, 1000);

                // Diyalog kutusunu sayfaya ekle
                document.body.appendChild(dialog);

                // Diyalog kutusunu kapatmak için arka plan ekleyelim
                const overlay = document.createElement('div');
                overlay.style.position = 'fixed';
                overlay.style.top = '0';
                overlay.style.left = '0';
                overlay.style.width = '100%';
                overlay.style.height = '100%';
                overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                overlay.style.zIndex = '999';

                // Arka plana tıklandığında diyalog kutusunu kapat
                overlay.addEventListener('click', () => {
                    document.body.removeChild(dialog);
                    document.body.removeChild(overlay);
                });

                // Arka planı sayfaya ekle
                document.body.appendChild(overlay);
            });

            // compare-panel'in altına karşılaştırma butonunu ekle
            comparePanel.appendChild(compareAllButton);
        }
        injectContent();
    `;

    // Oluşturulan JavaScript içeriğini gönder
    res.send(scriptContent);
});
// Sunucuyu başlat
app.listen(PORT, () => {
    console.log(`Sunucu http://localhost:${PORT} adresinde çalışıyor.`);
});


/*
const script = document.createElement('script');
script.src = 'http://localhost:8001/script.js';
document.body.appendChild(script);
*/







