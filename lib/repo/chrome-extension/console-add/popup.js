document.getElementById('injectButton').addEventListener('click', async () => {
  try {
    // Aktif sekmedeki içerik betiği çalıştırma
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    if (tab.id) {
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: injectScript
      });
      console.log("Script başarıyla enjekte edildi.");
    } else {
      console.error("Aktif sekme bulunamadı.");
    }
  } catch (error) {
    console.error("Hata:", error);
  }
});

document.getElementById('injectButton2').addEventListener('click', async () => {
  try {
    // Aktif sekmedeki içerik betiği çalıştırma
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    if (tab.id) {
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: injectScript2
      });
      console.log("Script başarıyla enjekte edildi.");
    } else {
      console.error("Aktif sekme bulunamadı.");
    }
  } catch (error) {
    console.error("Hata:", error);
  }
});

// Enjekte edilecek fonksiyon
function injectScript() {
  const script = document.createElement('script');
  script.src = 'http://localhost:4021/script.js?cli=67b75b1b6579206a6276c3d0&prod=tourComparison';
  script.setAttribute('data-key', '67b75b1b6579206a6276c3d0');
  document.body.appendChild(script);
}

function injectScript2() {
  const script = document.createElement('script');
  script.src = 'http://localhost:8001/script.js?cli=clienttest&prod=tourComparison';
  script.setAttribute('data-key', 'test');
  document.body.appendChild(script);
}

/*

  const script = document.createElement('script');
  script.src = 'https://tourcomparison.subanet.com/script.js?cli=67b75b1b6579206a6276c3d0&prod=tourComparison';
  script.setAttribute('data-key', '67b75b1b6579206a6276c3d0');
  document.body.appendChild(script);

*/