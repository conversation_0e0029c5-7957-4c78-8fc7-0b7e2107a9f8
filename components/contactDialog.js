import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Slide from '@mui/material/Slide';
import Image from "next/image";
import { CircularProgress } from '@mui/material';
import { GoogleAnalytics, sendGAEvent } from '@next/third-parties/google'
import TextField from '@mui/material/TextField';
import Container from '@mui/material/Container';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { Grid2 } from '@mui/material';
import Wave from 'react-wavify'

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />
})
const AlertDialogSlide = React.forwardRef(function AlertDialogSlide(props, ref) {
    const [open, setOpen] = React.useState(false);
    const [loading, setLoading] = React.useState(false);

    const [formData, setFormData] = React.useState({
        fullName: '',
        email: '',
        message: '',
    });

    React.useEffect(() => {
        if (open) {
            setFormData({
                fullName: '',
                email: '',
                message: ''
            })
        }
    }, [open]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prevState => ({
            ...prevState,
            [name]: value
        }));
        setErrors(prev => ({
            ...prev,
            [name]: false
        }));
    };

    React.useImperativeHandle(ref, () => ({
        async showModal(ix) {
            setOpen(true);
        },
    }));

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const [errors, setErrors] = React.useState({
        fullName: false,
        email: false
    });

    const validateEmail = (email) => {
        return email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        let hasErrors = false;
        let newErrors = { fullName: false, email: false };

        if (!formData.fullName.trim()) {
            newErrors.fullName = true;
            hasErrors = true;
        }
        if (!formData.email.trim() || !validateEmail(formData.email)) {
            newErrors.email = true;
            hasErrors = true;
        }

        setErrors(newErrors);

        if (!hasErrors) {

            setLoading(true);
            // console.log('Form Data:', JSON.stringify(formData, null, 2));
            // handleClose();
            try {
                const response = await fetch('/api/send-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-api-key': 'SubanetContactForm',
                    },
                    body: JSON.stringify(formData),
                });

                if (response.ok) {
                    console.log('Email sent successfully');

                    try { sendGAEvent('event', 'sendMessage', { value: new Date(Date.now()).toISOString(), formData: JSON.stringify(formData) }); }
                    catch (e) { console.error(e); }

                    handleClose();
                } else {
                    console.error('Failed to send email');
                    try { sendGAEvent('event', 'sendMessageFailed', { value: new Date(Date.now()).toISOString(), formData: JSON.stringify(formData) }); }
                    catch (e) { console.error(e); }
                }
            } catch (error) {
                try { sendGAEvent('event', 'sendMessageFailed', { value: new Date(Date.now()).toISOString(), formData: JSON.stringify(formData) }); }
                catch (e) { console.error(e); }
                console.error('Error:', error);
            } finally {
                setLoading(false);
            }

        }
    };
    return (
        <React.Fragment>
            {/* <Button variant="outlined" onClick={handleClickOpen} sx={{ m: 2 }}>
                {props.text}
            </Button> */}
            <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                onClose={handleClose}
                aria-describedby="alert-dialog-slide-description"
                maxWidth="md"
                PaperProps={{
                    component: 'form',
                    onSubmit: (event) => {
                        event.preventDefault();
                        const formData = new FormData(event.currentTarget);
                        const formJson = Object.fromEntries((formData).entries());
                        const email = formJson.email;
                        console.log(email);
                        handleClose();
                    },

                }}
            >
                <DialogTitle>{"Contact Us"}</DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-slide-description">
                        For further questions, including partnership opportunities, please email{' '}
                        <Box component="span" sx={{ color: '#3366FF' }}><EMAIL></Box> {' '}
                        or contact using our contact form.
                    </DialogContentText>
                </DialogContent>
                <DialogContent
                    sx={{
                        backgroundImage: 'url("/world.png")',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat',
                        backgroundColor: 'rgba(255,255,255,0.95)',
                        minHeight: 500,
                        position: 'relative',
                        '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.99), rgba(255, 255, 255, 0.01))',
                            backgroundColor: 'rgba(255,255,255,0.1)',
                            zIndex: 0
                        },
                        '& > *': {
                            position: 'relative',
                            zIndex: 1
                        }
                    }}>
                    <Grid2 container spacing={2} columns={12}>
                        <Grid2 size={{ xs: 12, md: 6 }}>
                            <Box sx={{ mt: 2 }}>

                                <Typography variant="h4" component="h4" sx={{
                                    mt: 2,
                                    color: '#000',
                                    // textShadow: '1px 0px 0px rgba(0,0,0, 1)'
                                }}>
                                    Subanet Ltd
                                </Typography>
                                <Typography variant="body1" component="p">
                                    incorporated on 18 November 2020 with the registered office located in London, Greater London.
                                </Typography>
                                <Typography variant="h6" component="h6" sx={{
                                    mt: 2,
                                    color: '#000',
                                    fontWeight: 'bold',
                                    letterSpacing: '2px',
                                    // textShadow: '1px 0px 0px rgba(0,0,0, 1)'
                                }}>
                                    Offices In:
                                </Typography>
                                <Typography variant="body1" component="p"
                                    sx={{
                                        color: '#2196f3',
                                        letterSpacing: '1px',
                                        textShadow: '1px 0px 0px rgba(0,0,0, 1)'
                                    }}>
                                    London
                                </Typography>
                                <Typography variant="body1" component="p"
                                    sx={{
                                        color: '#2196f3',
                                        letterSpacing: '1px',
                                        textShadow: '1px 0px 0px rgba(0,0,0, 1)'
                                    }}>
                                    Istanbul
                                </Typography>
                            </Box>
                        </Grid2>

                        <Grid2 key={'formField'} size={{ xs: 12, md: 6 }}>
                            <Box sx={{
                                mt: 2,
                                p: 2,
                                borderRadius: 1,
                                backgroundColor: 'rgba(255,255,255,0.4)',
                                backdropFilter: 'blur(5px)'
                            }}>
                                <TextField
                                    error={errors.fullName}
                                    helperText={errors.fullName ? 'Full name is required' : ''}
                                    autoFocus
                                    required
                                    margin="dense"
                                    id="fullName"
                                    name="fullName"
                                    label="Full Name"
                                    type="text"
                                    fullWidth
                                    variant="standard"
                                    value={formData.fullName}
                                    onChange={handleInputChange}
                                    sx={{
                                        '& label': { mb: 2, color: '#1976d2' },
                                        '& .MuiInput-underline:before': { borderBottomColor: '#1976d2' }
                                    }}

                                />

                                <TextField
                                    error={errors.email}
                                    helperText={errors.email ? 'Valid email is required' : ''}
                                    autoFocus
                                    required
                                    margin="dense"
                                    id="name"
                                    name="email"
                                    label="Email Address"
                                    type="email"
                                    fullWidth
                                    variant="standard"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    sx={{ mb: 2 }}
                                />

                                <TextField
                                    fullWidth
                                    label="Your Message"
                                    margin="dense"
                                    id='message'
                                    name='message'
                                    multiline
                                    rows={4}
                                    placeholder=" "
                                    variant="outlined"
                                    // variant="outline"
                                    value={formData.message}
                                    onChange={handleInputChange}
                                    required
                                    sx={{ mb: 4 }}
                                />


                                <Box sx={{ display: 'flex', justifyContent: 'flex-end', }}>
                                    <Button variant="outlined"

                                        disabled={loading}
                                        startIcon={loading ? <CircularProgress size={20} /> : null}
                                        onClick={handleSubmit} sx={{
                                            bgcolor: '#3366FF',
                                            '&:hover': {

                                                color: 'white',
                                                bgcolor: '#2952CC'
                                            }, textAlign: 'center', backgroundColor: 'rgba(255,255,255,0.5)', backdropFilter: 'blur(5px)'
                                        }}>
                                        {loading ? 'Sending...' : 'Send Message'}
                                    </Button>
                                </Box>

                            </Box>
                        </Grid2>
                    </Grid2>

                </DialogContent>
                <Box sx={{ maxHeight: 50, overflow: 'hidden' }}>

                    <Wave filxl='#f79902' fill='#bababa'
                        paused={false}
                        style={{ display: 'flex' }}
                        options={{
                            height: 10,
                            amplitude: 20,
                            speed: 0.15,
                            points: 3
                        }}
                    />
                </Box>
                <DialogActions sx={{ position: 'absolute', bottom: 0, right: 0, left: 0, zIndex: 1, backdropFilter: 'blur(5px)' }}>

                    <Button onClick={handleClose}>Close</Button>
                </DialogActions>
            </Dialog>
        </React.Fragment>
    );
})

module.exports = { AlertDialogSlide };

